import { defineConfig, loadEnv } from 'vite'
import { crx } from '@crxjs/vite-plugin'
import vue from '@vitejs/plugin-vue'
import manifest from './src/manifest'
import UnoCSS from 'unocss/vite'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const production = mode === 'production'
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    build: {
      cssCodeSplit: true,
      emptyOutDir: true,
      outDir: 'build',
      rollupOptions: {
        input: {
          popup: resolve(__dirname, 'popup.html')
        },
        output: {
          chunkFileNames: 'assets/chunk-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        },
      },
    },
    plugins: [crx({ manifest }), vue(), UnoCSS()],
    server: {
      port: 5173,
      strictPort: true,
      hmr: {
        port: 5173
      }
    },
    legacy: {
      skipWebSocketTokenCheck: true,
    }
  }
})
