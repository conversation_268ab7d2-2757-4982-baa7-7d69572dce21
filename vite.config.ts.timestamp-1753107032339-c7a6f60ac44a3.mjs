// vite.config.ts
import { defineConfig, loadEnv } from "file:///C:/Users/<USER>/code/auto-tiktok-seller/node_modules/.pnpm/vite@5.4.18_@types+node@22.15.2/node_modules/vite/dist/node/index.js";
import { crx } from "file:///C:/Users/<USER>/code/auto-tiktok-seller/node_modules/.pnpm/@crxjs+vite-plugin@2.0.0-beta.32/node_modules/@crxjs/vite-plugin/dist/index.mjs";
import vue from "file:///C:/Users/<USER>/code/auto-tiktok-seller/node_modules/.pnpm/@vitejs+plugin-vue@4.6.2_vi_1eb6c7e8b3a2dd2c4abd6c635790cdcd/node_modules/@vitejs/plugin-vue/dist/index.mjs";

// src/manifest.ts
import { defineManifest } from "file:///C:/Users/<USER>/code/auto-tiktok-seller/node_modules/.pnpm/@crxjs+vite-plugin@2.0.0-beta.32/node_modules/@crxjs/vite-plugin/dist/index.mjs";

// package.json
var package_default = {
  name: "auto-tiktok-seller",
  displayName: "TikTok-\u5927\u5496",
  version: "1.0.28",
  author: "xifan",
  description: "TikTok \u5546\u57CE\u81EA\u52A8\u63D0\u62A5\u5DE5\u5177\uFF0C\u667A\u80FD\u9009\u62E9\u7B56\u7565\uFF0C\u6574\u9875\u4E00\u952E\u63D0\u62A5\uFF0C\u53D1\u8D22\u5FEB\u4EBA\u4E00\u6B65\uFF01",
  type: "module",
  license: "MIT",
  keywords: [
    "chrome-extension",
    "vue",
    "vite",
    "create-chrome-ext"
  ],
  engines: {
    node: ">=14.18.0"
  },
  scripts: {
    dev: "vite",
    build: "vite build",
    "type-check": "vue-tsc --noEmit",
    preview: "vite preview",
    fmt: "prettier --write '**/*.{vue,ts,json,css,scss,md}'",
    zip: "npm run build && node src/zip.js",
    "generate-license": "bun tools/licenseGenerator.ts"
  },
  dependencies: {
    "@types/crypto-js": "^4.2.2",
    "crypto-js": "^4.2.0",
    marked: "^15.0.11",
    vue: "^3.3.4"
  },
  devDependencies: {
    "@crxjs/vite-plugin": "^2.0.0-beta.26",
    "@iconify-json/ri": "^1.2.5",
    "@types/chrome": "^0.0.246",
    "@types/node": "^22.15.2",
    "@vitejs/plugin-vue": "^4.4.0",
    gulp: "^5.0.0",
    "gulp-zip": "^6.0.0",
    prettier: "^3.0.3",
    typescript: "^5.2.2",
    unocss: "66.1.0-beta.12",
    vite: "^5.4.10",
    "vue-tsc": "^1.8.22"
  }
};

// src/manifest.ts
var isDev = process.env.NODE_ENV == "development";
var manifest_default = defineManifest({
  name: `${package_default.displayName || package_default.name}${isDev ? ` \u27A1\uFE0F Dev` : ""}`,
  description: package_default.description,
  version: package_default.version,
  author: {
    email: "D4K8888"
  },
  manifest_version: 3,
  icons: {
    16: "img/logo-16.png",
    32: "img/logo-32.png",
    48: "img/logo-48.png",
    128: "img/logo-128.png"
  },
  action: {
    default_popup: "popup.html",
    default_icon: "img/logo-48.png"
  },
  background: {
    service_worker: "src/background/index.ts",
    type: "module"
  },
  content_scripts: [
    {
      matches: [
        "https://seller-sg.tiktok.com/product/opportunity*",
        "https://seller-ph.tiktok.com/product/opportunity*",
        "https://seller-id.tiktok.com/product/opportunity*",
        "https://seller-id.tokopedia.com/product/opportunity*",
        "https://seller-my.tiktok.com/product/opportunity*",
        "https://seller-th.tiktok.com/product/opportunity*",
        "https://seller-vn.tiktok.com/product/opportunity*",
        "https://seller.tiktokshopglobalselling.com/product/opportunity*",
        "https://seller.tiktokglobalshop.com/product/opportunity*",
        "https://seller-us.tiktok.com/product/opportunity*",
        "https://seller.us.tiktokglobalshop.com/product/opportunity*",
        "https://seller.us.tiktokshopglobalselling.com/product/opportunity",
        "https://seller.eu.tiktokglobalshop.com/product/opportunity*",
        "https://seller.eu.tiktokshopglobalselling.com/product/opportunity*",
        "https://seller-uk.tiktok.com/product/opportunity*",
        "https://seller-jp.tiktok.com/product/opportunity*",
        "https://seller-de.tiktok.com/product/opportunity*",
        "https://*/*",
        "http://*/*"
      ],
      js: ["src/contentScript/core/index.ts"]
    }
  ],
  web_accessible_resources: [
    {
      resources: ["img/logo-16.png", "img/logo-32.png", "img/logo-48.png", "img/logo-128.png"],
      matches: []
    }
  ],
  permissions: ["storage", "system.cpu", "system.display", "tabs"],
  host_permissions: []
});

// vite.config.ts
import UnoCSS from "file:///C:/Users/<USER>/code/auto-tiktok-seller/node_modules/.pnpm/unocss@66.1.0-beta.12_postc_90f1caa469821355ff72436a032da6be/node_modules/unocss/dist/vite.mjs";
import { resolve } from "path";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\code\\auto-tiktok-seller";
var vite_config_default = defineConfig(({ mode }) => {
  const production = mode === "production";
  const env = loadEnv(mode, process.cwd(), "");
  return {
    build: {
      cssCodeSplit: true,
      emptyOutDir: true,
      outDir: "build",
      rollupOptions: {
        input: {
          popup: resolve(__vite_injected_original_dirname, "popup.html")
        },
        output: {
          chunkFileNames: "assets/chunk-[hash].js",
          entryFileNames: "assets/[name]-[hash].js",
          assetFileNames: "assets/[name]-[hash].[ext]"
        }
      }
    },
    plugins: [crx({ manifest: manifest_default }), vue(), UnoCSS()],
    server: {
      port: 5173,
      strictPort: true,
      hmr: {
        port: 5173
      }
    },
    legacy: {
      skipWebSocketTokenCheck: true
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
