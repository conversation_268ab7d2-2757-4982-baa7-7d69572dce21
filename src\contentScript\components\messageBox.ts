import { getWechatNumber } from '../utils/utils'

interface MessageBoxOptions {
  title?: string
  message: string
  type?: 'success' | 'error' | 'info' | 'warning'
  showWechat?: boolean
  onConfirm?: () => void | Promise<void>
  onClose?: () => void | Promise<void>
}

// 颜色系统
const colors = {
  success: {
    bg: '#ffffff',
    border: '#10b981',
    text: '#047857',
    button: '#10b981',
    buttonHover: '#047857',
    lightBg: '#d1fae5'
  },
  error: {
    bg: '#ffffff',
    border: '#ef4444',
    text: '#b91c1c',
    button: '#ef4444',
    buttonHover: '#b91c1c',
    lightBg: '#fee2e2'
  },
  info: {
    bg: '#ffffff',
    border: '#3b82f6',
    text: '#1d4ed8',
    button: '#3b82f6',
    buttonHover: '#1d4ed8',
    lightBg: '#dbeafe'
  },
  warning: {
    bg: '#ffffff',
    border: '#f59e0b',
    text: '#b45309',
    button: '#f59e0b',
    buttonHover: '#b45309',
    lightBg: '#fef3c7'
  }
}

const createMessageBoxElement = async (options: MessageBoxOptions) => {
  const type = options.type || 'info'
  const colorScheme = colors[type]

  // 创建遮罩层
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999999;
  `

  // 创建消息框
  const messageBox = document.createElement('div')
  messageBox.style.cssText = `
    background: ${colorScheme.bg};
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -1px rgba(0, 0, 0, 0.05);
    max-width: 360px;
    width: 90%;
    position: relative;
    border: 1px solid ${colorScheme.border}30;
  `

  // 标题样式
  const title = document.createElement('div')
  title.style.cssText = `
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: ${colorScheme.text};
    display: flex;
    align-items: center;
  `
  title.textContent = options.title || '提示'

  // 消息内容
  const content = document.createElement('div')
  content.style.cssText = `
    font-size: 14px;
    line-height: 1.6;
    color: #4b5563;
    margin-bottom: ${options.showWechat ? '16px' : '20px'};
  `
  content.textContent = options.message

  // 如果需要显示微信号
  let wechatInfo: HTMLDivElement | undefined
  if (options.showWechat) {
    const wechatNumber = await getWechatNumber()

    wechatInfo = document.createElement('div')
    wechatInfo.style.cssText = `
      font-size: 14px;
      line-height: 1.6;
      color: #4b5563;
      margin-bottom: 20px;
      padding: 10px 12px;
      background: ${colorScheme.lightBg};
      border-radius: 6px;
      border: none;
      display: flex;
      align-items: center;
      gap: 8px;
    `
    wechatInfo.textContent = '续费请添加微信：' + wechatNumber
  }

  // 确认按钮
  const confirmButton = document.createElement('button')
  confirmButton.id = 'message-box-confirm'
  confirmButton.style.cssText = `
    width: 100%;
    background: ${colorScheme.button};
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    font-weight: 500;
  `
  confirmButton.textContent = '确定'
  confirmButton.onmouseover = () => {
    confirmButton.style.backgroundColor = colorScheme.buttonHover
    confirmButton.style.transform = 'translateY(-1px)'
  }
  confirmButton.onmouseout = () => {
    confirmButton.style.backgroundColor = colorScheme.button
    confirmButton.style.transform = 'translateY(0)'
  }

  // 点击事件处理
  confirmButton.onclick = () => {
    document.body.removeChild(overlay)
  }

  // 组装消息框
  messageBox.appendChild(title)
  messageBox.appendChild(content)
  if (wechatInfo) {
    messageBox.appendChild(wechatInfo)
  }
  messageBox.appendChild(confirmButton)
  overlay.appendChild(messageBox)

  return overlay
}

export const messageBox = {
  show: async (options: MessageBoxOptions): Promise<void> => {
    return new Promise((resolve) => {
      createMessageBoxElement(options).then(element => {
        document.body.appendChild(element)
        const confirmButton = element.querySelector('#message-box-confirm') as HTMLButtonElement
        if (confirmButton) {
          const originalOnClick = confirmButton.onclick
          confirmButton.onclick = async () => {
            if (originalOnClick) {
              originalOnClick.call(confirmButton, new MouseEvent('click'))
            }
            if (options.onConfirm) {
              await options.onConfirm()
            }
            resolve()
          }
        }

        // 添加关闭按钮点击事件
        const closeButton = element.querySelector('button:not(#message-box-confirm)') as HTMLButtonElement
        if (closeButton) {
          const originalOnClick = closeButton.onclick
          closeButton.onclick = async () => {
            if (originalOnClick) {
              originalOnClick.call(closeButton, new MouseEvent('click'))
            }
            if (options.onClose) {
              await options.onClose()
            }
            resolve()
          }
        }
      })
    })
  },

  error: async (message: string, showWechat: boolean = false, onConfirm?: () => void | Promise<void>, onClose?: () => void | Promise<void>): Promise<void> => {
    return messageBox.show({
      title: '错误提示',
      message,
      type: 'error',
      showWechat,
      onConfirm,
      onClose
    })
  },

  info: async (message: string, showWechat: boolean = false, onConfirm?: () => void | Promise<void>, onClose?: () => void | Promise<void>): Promise<void> => {
    return messageBox.show({
      title: '提示信息',
      message,
      type: 'info',
      showWechat,
      onConfirm,
      onClose
    })
  },

  success: async (message: string, showWechat: boolean = false, onConfirm?: () => void | Promise<void>, onClose?: () => void | Promise<void>): Promise<void> => {
    return messageBox.show({
      title: '成功提示',
      message,
      type: 'success',
      showWechat,
      onConfirm,
      onClose
    })
  },

  warning: async (message: string, showWechat: boolean = false, onConfirm?: () => void | Promise<void>, onClose?: () => void | Promise<void>): Promise<void> => {
    return messageBox.show({
      title: '警告提示',
      message,
      type: 'warning',
      showWechat,
      onConfirm,
      onClose
    })
  }
}
