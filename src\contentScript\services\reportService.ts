/**
 * @module ReportService
 * @description 商品提报服务模块
 * 负责处理商品提报的核心逻辑，包括：
 * - 前置检查
 * - 主页面操作
 * - 侧边栏操作
 * - 商品选择
 * - 提报流程控制
 */

import { _selectors } from '../dom/selectors'
import { toast } from '../components/toast'
import {
  waitForElementDisappear,
  clickElementWithOperationDelay,
  smartDelay,
  waitForElementAppear,
} from '../utils/utils'
import { panelState } from '../components/state'
import { selectItems } from './itemSelectService'
import { authGuard } from '../../utils/auth'
import { queryStartTime, saveStartTime } from '../../utils/storage'
import { logger } from '../utils/logger'
import { createAbortController, incrementCount, getTotalCount } from '../core/taskManager'
import { messageBox } from '../components/messageBox'
import { authPanel } from '../components/AuthPanel'
// !================ 工具函数 =====================

// !================ 主页面前置检查阶段 ================

/**
 * * 检查当前是否需要选择商品步骤
 * @returns
 */
function maybeNotHaveSelectGoodsBtn(): boolean {
  const currentUrl = window.location.href
  return (
    // 匹配以下网址:
    // https://seller-us.tiktok.com
    // https://seller.us.tiktokglobalshop.com
    // https://seller.us.tiktokshopglobalselling.com
    // https://seller.eu.tiktokglobalshop.com
    // https://seller.eu.tiktokshopglobalselling.com
    // https://seller.tiktokglobalshop.com
    /seller(?:-(?:us|eu)\.tiktok\.com|\.(?:(?:us|eu)\.)?tiktok(?:globalshop|shopglobalselling)\.com)/.test(currentUrl)
  )
}

/**
 * * 获取并验证按钮
 * @param customButton - 自定义按钮元素
 * @param signal - 可选的终止信号
 * @throws {Error} 当按钮验证失败时抛出错误
 */
async function validateButton(customButton: HTMLElement, signal?: AbortSignal): Promise<void> {
  const reportButtonId = customButton.getAttribute('data-report-btn-id')

  if (!reportButtonId) {
    toast.warning('无法开始自动提报，请刷新页面重试', 2000)
    throw new Error('无法开始自动提报，请刷新页面重试')
  }

  // 检查原始按钮是否可用
  const originalButton = document.getElementById(reportButtonId)
  if (!originalButton || !originalButton.isConnected) {
    throw new Error('原始按钮不可用，请刷新页面重试')
  }
}

/**
 * * 检查侧边栏状态
 * @param signal - 可选的终止信号
 * @throws {Error} 当侧边栏未关闭时抛出错误
 */
async function checkSidebarClosed(signal?: AbortSignal): Promise<boolean> {
  const sidePanel = document.querySelector(_selectors.drawerWrapper)
  if (sidePanel && getComputedStyle(sidePanel).display !== 'none') {
    await closeSidePanel(signal)
  }
  return true
}

/**
 * * 前置检查
 * @param customButton - 自定义按钮元素
 * @param signal - 可选的终止信号
 * @throws {Error} 当检查失败时抛出错误
 */
async function preCheckForReport(customButton: HTMLElement, signal?: AbortSignal): Promise<void> {
  // 0. 检查并记录起始时间
  const startTime = await queryStartTime()
  if (!startTime) {
    // 第一次使用，记录起始时间
    await saveStartTime(Date.now().toString())
  }

  // 1. 鉴权检查
  const isAuthorized = await authGuard()
  if (!isAuthorized) {
    await messageBox.error('请先完成授权后再使用此功能', true, async () => {
      await authPanel.show()
    })
    throw new Error('未授权')
  }

  // 2. 先检查布局，这个不需要taskId
  const layoutMode = await waitForElementAppear(
    _selectors.checkGridLayout,
    panelState.value.timeout,
    signal,
  )
  if (!layoutMode) {
    toast.warning('请在网格模式下使用', 2000)
    throw new Error('请在网格模式下使用')
  }

  // 3. 获取按钮ID
  const reportButtonId = customButton.getAttribute('data-report-btn-id')
  if (!reportButtonId) {
    throw new Error('无法开始自动提报，请刷新页面重试')
  }

  // 4. 获取原始按钮
  const originalButton = document.getElementById(reportButtonId)
  if (!originalButton) {
    toast.warning('无法找到原始按钮，请刷新页面重试', 2000)
    throw new Error('无法找到原始按钮，请刷新页面重试')
  }

  await validateButton(customButton, signal)
  await checkSidebarClosed(signal)
}

/**
 * * 准备点击操作
 * @param btnId - 按钮ID
 * @param signal - 可选的终止信号
 */
async function clickOriginalButton(btnId: string, signal?: AbortSignal) {
  await clickElementWithOperationDelay(`#${btnId}`, panelState.value.timeout, signal)
}

/**
 * * 等待侧边栏打开
 * @param signal - 可选的终止信号
 * @throws {Error} 当侧边栏无法打开时抛出错误
 */
async function waitForSidePanel(signal?: AbortSignal): Promise<boolean> {
  const sidePanel = await waitForElementAppear(
    _selectors.drawerWrapper,
    panelState.value.timeout,
    signal,
  )
  if (!sidePanel) {
    logger.error('侧边栏未打开')
    throw new Error('无法开始自动提报，请刷新页面重试')
  }
  return true
}

/**
 * * 等待侧边栏关闭
 * @param signal - 可选的终止信号
 * @throws {Error} 当侧边栏无法关闭时抛出错误
 */
async function waitForSidePanelClosed(signal?: AbortSignal): Promise<boolean> {
  const sidePanel = await waitForElementDisappear(
    _selectors.drawerWrapper,
    panelState.value.timeout,
    signal,
  )
  return sidePanel
}

/**
 * * 执行主页面操作
 * @param customButton - 自定义按钮元素
 * @param signal - 可选的终止信号
 */
async function executeMainPageOperation(
  customButton: HTMLElement,
  signal?: AbortSignal,
): Promise<boolean> {
  try {
    // 1. 点击按钮

    await clickOriginalButton(customButton.getAttribute('data-report-btn-id') || '', signal)
    await waitForSidePanel(signal)
    return true
  } catch (error) {
    return false
  }
}

//! ================ 侧边栏操作阶段 ================

/**
 * * 等待商品列表加载完成
 * @param signal - 可选的终止信号
 * @throws {Error} 当加载超时或操作被终止时抛出错误
 */
async function waitForGoodsListComplete(signal?: AbortSignal): Promise<boolean> {

  if (signal?.aborted) {
    throw new Error('操作被终止')
  }

  await waitForElementDisappear(_selectors.checkLoading, panelState.value.timeout, signal)

  if (maybeNotHaveSelectGoodsBtn()) {

    const goodsList = await waitForElementAppear(
      _selectors.checkGoodsItem,
      panelState.value.timeout,
      signal,
    )
    if (!goodsList) {
      return false
    }
  } else {

    const selectButton = await waitForElementAppear(
      _selectors.getSelectButton,
      panelState.value.timeout,
      signal,
    )
    if (!selectButton) {
      return false
    } else {
      await clickElementWithOperationDelay(
        _selectors.getSelectButton,
        panelState.value.timeout,
        signal,
      )
      const goodsList = await waitForElementAppear(
        _selectors.checkGoodsItem,
        panelState.value.timeout,
        signal,
      )
      if (!goodsList) {
        return false
      }
    }
  }

  await smartDelay(panelState.value.operationInterval, signal)
  return true
}

/**
 * * 获取分页信息
 * @param signal - 可选的终止信号
 * @returns 包含当前页码和最大页码的对象
 */
async function getGoodsPageInfo(
  signal?: AbortSignal,
): Promise<{ currentPage: number; maxPage: number }> {
  // 等待分页容器

  const paginationContainer = await waitForElementAppear(
    _selectors.getOneGoodsPagination,
    panelState.value.timeout,
    signal,
  )
  if (!paginationContainer) {
    return { currentPage: 1, maxPage: 1 }
  }

  // 获取当前页码

  const currentPageElement = await waitForElementAppear(
    _selectors.paginationCurrentPage,
    panelState.value.timeout,
    signal,
  )
  if (!currentPageElement) {
    return { currentPage: 1, maxPage: 1 }
  }

  const currentPageText = currentPageElement.textContent

  let currentPage = parseInt(currentPageText || '1', 10)
  let maxPage = 1

  // 获取所有页码按钮
  const pageButtons = Array.from(
    paginationContainer.querySelectorAll(_selectors.paginationPageButton),
  )
  if (pageButtons.length > 0) {
    // 获取所有页码数字并找到连续序列的最后一个数字
    const pageNumbers = pageButtons
      .map((btn) => parseInt(btn.textContent || '0', 10))
      .filter((num) => !isNaN(num) && num > 0)
      .sort((a, b) => a - b)

    // 找到第一个不连续的数字的位置，如果没有不连续的数字则返回数组长度
    const breakIndex = pageNumbers.findIndex(
      (num, idx) => idx < pageNumbers.length - 1 && pageNumbers[idx + 1] !== num + 1,
    )

    // 如果找到不连续的数字，返回它的前一个数字，否则返回最后一个数字
    maxPage = breakIndex === -1 ? pageNumbers[pageNumbers.length - 1] : pageNumbers[breakIndex]
  }

  // 验证分页值
  if (isNaN(currentPage) || currentPage < 1) {
    currentPage = 1
  }

  await closeSidePanel()
  return { currentPage, maxPage }
}

/**
 * * 跳转到指定页面
 * @param targetPage - 目标页码
 * @param signal - 可选的终止信号
 * @throws {Error} 当页码超出范围时抛出错误
 */
async function jumpToPage(
  targetPage: number,
  maxPage: number,
  signal?: AbortSignal,
): Promise<number> {
  if (targetPage > maxPage) {
    await closeSidePanel(signal)
    throw new Error(`页码超出范围：目标页 ${targetPage} > 最大页 ${maxPage}`)
  }

  // 获取当前页码
  const currentPageElement = await waitForElementAppear(
    _selectors.paginationCurrentPage,
    panelState.value.timeout,
    signal,
  )
  const currentPage = parseInt(currentPageElement?.textContent || '1')

  // 如果已经在目标页，直接返回
  if (currentPage === targetPage) {
    return currentPage
  }

  // 查找所有页码按钮
  const pageButtons = Array.from(document.querySelectorAll(_selectors.paginationPageButton))
  let targetButton: HTMLElement | null = null

  // 遍历找到目标页码按钮
  for (const button of pageButtons) {
    if (button.textContent?.trim() === targetPage.toString()) {
      targetButton = button as HTMLElement
      break
    }
  }

  if (!targetButton) {
    return -1
  }

  // 点击找到的目标按钮
  await smartDelay(panelState.value.operationInterval, signal)
  targetButton.click()
  await smartDelay(panelState.value.operationInterval, signal)

  return targetPage
}

/**
 * * 选择商品
 * @param signal - 可选的终止信号
 * @returns 选中的商品数量
 */
async function selectGoods(signal?: AbortSignal) {
  // 先等待并检查元素是否存在
  await waitForElementDisappear(_selectors.checkLoading, panelState.value.timeout, signal)
  await waitForElementAppear(_selectors.checkGoodsItemContainer, panelState.value.timeout, signal)
  const checkElement = await waitForElementAppear(
    _selectors.checkGoodsItemContainer,
    panelState.value.timeout,
    signal,
  )
  if (!checkElement) {
    throw new Error('未找到可选择的商品')
  }

  // 确认元素存在后获取所有商品
  const items = document.querySelectorAll(_selectors.checkGoodsItemContainer)
  if (!items.length) {
    throw new Error('未找到可选择的商品')
  }

  // 尝试使用配置的策略选择商品
  let selectedItems = selectItems(
    items,
    panelState.value.selectStrategy,
    panelState.value.customStartIndex,
  )

  let selectedCount = 0

  // 尝试点击选中的商品
  for (const item of selectedItems) {
    try {
      const checkbox = item.querySelector(_selectors.checkGoodsItem) as HTMLInputElement
      if (checkbox && !checkbox.checked) {
        checkbox.click()
        await smartDelay(panelState.value.operationInterval, signal)
        // 点击后检查是否真正选中
        if (checkbox.checked) {
          selectedCount++
        }
      }
    } catch (error) {
      continue
    }
  }

  // 如果原策略没有成功选中任何商品，fallback到all策略
  if (selectedCount === 0) {
    // 使用all策略重新选择
    selectedItems = selectItems(items, 'all')

    // 重新尝试点击所有商品
    for (const item of selectedItems) {
      try {
        const checkbox = item.querySelector(_selectors.checkGoodsItem) as HTMLInputElement
        if (checkbox && !checkbox.checked) {
          checkbox.click()
          await smartDelay(panelState.value.operationInterval, signal)
          // 点击后检查是否真正选中
          if (checkbox.checked) {
            selectedCount++
          }
        }
      } catch (error) {
        continue
      }
    }
  }

  // 获取最终选中的数量
  const checkedCount = await getCheckedGoodsCount(signal)

  // 如果没有选中任何商品，关闭侧边栏并终止任务
  if (checkedCount === 0) {
    await closeSidePanel(signal)
    return 0 // 返回0表示没有选中任何商品
  }

  return checkedCount
}

/**
 * * 获取已选商品数量
 * @param signal - 可选的终止信号
 * @returns 已选商品数量
 */
async function getCheckedGoodsCount(signal?: AbortSignal) {
  const checkedItems = document.querySelectorAll(_selectors.checkGoodsItemChecked)
  return checkedItems.length
}

/**
 * * 关键词增强
 * @param signal - 可选的终止信号
 * @returns 是否与面板状态一致
 */
async function useKeywordEnchancement(signal?: AbortSignal) {
  // 等待并获取开关元素

  const useKeywordsSwitch = await waitForElementAppear(
    _selectors.useKeywords,
    panelState.value.timeout,
    signal,
  )

  if (!useKeywordsSwitch) {
    return false
  }

  // 获取当前状态
  const isCurrentlyEnabled = useKeywordsSwitch.classList.contains('core-switch-checked')

  const shouldBeEnabled = panelState.value.reportUseKeywords

  // 如果当前状态与面板配置不一致，需要点击切换
  if (isCurrentlyEnabled !== shouldBeEnabled) {
    await smartDelay(panelState.value.operationInterval, signal)
    useKeywordsSwitch.click()
    await smartDelay(panelState.value.operationInterval, signal)
  }

  return true
}

/**
 * * 点击下一步
 * @param signal - 可选的终止信号
 * @returns 是否可以继续
 */
async function clickNextStep(signal?: AbortSignal): Promise<boolean> {
  // 检查按钮是否被禁用
  const disabledButton = document.querySelector(_selectors.nextStepButtonDisabled)
  if (disabledButton) {
    await closeSidePanel(signal)
    return false
  }

  // 按钮可用时执行点击
  await clickElementWithOperationDelay(_selectors.nextStepButton, panelState.value.timeout, signal)
  await smartDelay(panelState.value.operationInterval, signal)
  return true
}

/**
 * ? 关闭侧边栏
 * @param signal - 可选的终止信号
 * @throws {Error} 当无法找到关闭按钮时抛出错误
 */
async function closeSidePanel(signal?: AbortSignal) {
  const closeButton = await waitForElementAppear(
    _selectors.closeSidePanelButton,
    panelState.value.timeout,
    signal,
  )
  if (!closeButton) {
    throw new Error('无法找到关闭按钮')
  }

  await clickElementWithOperationDelay(
    _selectors.closeSidePanelButton,
    panelState.value.timeout,
    signal,
  )
  await smartDelay(1, signal) // 等待关闭动画
}

/**
 * * 执行侧边栏操作
 * @param signal - 可选的终止信号
 * @returns 操作是否成功
 */
async function executeSidebarOperation(
  index: number,
  page: number,
  maxPage: number,
  signal?: AbortSignal,
): Promise<boolean> {
  try {
    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 1. 等待商品列表加载完成
    const goodsListComplete = await waitForGoodsListComplete(signal)
    if (!goodsListComplete) {
      logger.error(`商品卡片${index}第${page}页商品列表加载失败`)
      return false
    } else {
      logger.info(`商品卡片${index}第${page}页商品列表加载成功`)
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 2. 跳转到目标页
    const targetPage = await jumpToPage(page, maxPage, signal)
    if (targetPage === -1) {
      logger.info(`商品卡片${index}第${page}页跳转失败`)
      return false
    } else {
      logger.info(`商品卡片${index}第${page}页跳转成功`)
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 3. 选择商品
    const selectedCount = await selectGoods(signal)

    // 增加计数
    incrementCount(selectedCount)

    // 显示总提报数，无论是否选中商品
    const totalCount = getTotalCount()
    toast.success(
      `商品卡片${index}第${page}页提报${selectedCount}个，累计提报 ${totalCount} 个商品`,
      2000,
    )
    logger.success(
      `商品卡片${index}第${page}页提报${selectedCount}个，累计提报 ${totalCount} 个商品`,
    )

    if (selectedCount === 0) {
      logger.warning(`商品卡片${index}第${page}页没有选中任何商品`)
      return false
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 4. 检查按钮状态
    const primaryButton = await waitForElementAppear(_selectors.nextStepButton, 1, signal)
    if (!primaryButton) {
      logger.info(`商品卡片${index}第${page}页未找到主按钮`)
      return false
    } else {
      logger.info(`商品卡片${index}第${page}页主按钮状态正常`)
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 获取按钮文本内容
    const buttonText = primaryButton.textContent?.trim().toLowerCase() || ''

    if (buttonText === '下一步' || buttonText === 'next') {
      // 存在下一步按钮，走原有流程
      const canContinue = await clickNextStep(signal)
      logger.info(`商品卡片${index}第${page}页点击下一步${canContinue ? '成功' : '失败'}`)
      if (!canContinue) {
        return false
      }
      // 同步关键词增强状态
      await useKeywordEnchancement(signal)
      logger.info(`商品卡片${index}第${page}页关键词增强状态同步完成`)
    } else if (buttonText === '提交' || buttonText === 'submit') {
      // 直接进入提交流程
      logger.info(`商品卡片${index}第${page}页直接进入提交流程`)
    } else {
      logger.info(`商品卡片${index}第${page}页按钮文本异常: ${buttonText}`)
      return false
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 5. 检查提交按钮状态
    let isDisabled = document.querySelector(_selectors.onePageSubmitButtonDisabled) !== null
    if (isDisabled) {
      const keywordSwitch = document.querySelector(_selectors.useKeywords) as HTMLElement
      if (keywordSwitch?.classList.contains('core-switch-checked')) {
        logger.warning(`商品卡片${index}第${page}页关键词增强状态开启时无法提交，尝试关闭`)
        keywordSwitch.click()
        await smartDelay(
          panelState.value.operationInterval > 0 ? panelState.value.operationInterval : 1,
          signal,
        )
        isDisabled = document.querySelector(_selectors.onePageSubmitButtonDisabled) !== null
        if (isDisabled) {
          logger.warning(`商品卡片${index}第${page}页关键词增强状态关闭后无法提交，放弃本页提报`)
          await closeSidePanel(signal)
          return false
        }
      } else {
        await closeSidePanel(signal)
        return false
      }
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 6. 点击提交按钮
    await clickElementWithOperationDelay(
      _selectors.onePageSubmitButton,
      panelState.value.operationInterval > 0 ? panelState.value.operationInterval : 1,
      signal,
    )
    logger.info(`商品卡片${index}第${page}页提交按钮点击成功`)
    return true
  } catch (error) {
    if (error instanceof Error && error.message === 'Operation aborted') {
      throw error
    }
    return false
  }
}

// !================ 商品提报流程 =====================
/**
 * * 单个商品提报的核心流程
 * ! 重点函数，优先调试
 * @param customButton - 自定义按钮元素
 * @param signal - 可选的终止信号
 * @returns 提报是否成功
 */
export async function reportOneGoods(
  customButton: HTMLElement,
  signal?: AbortSignal,
): Promise<boolean> {
  try {
    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    // 1. 前置检查和任务初始化
    const customButtonId = customButton.getAttribute('data-report-btn-id') || ''
    const index = parseInt(customButtonId.split('-').pop() || '0')
    await preCheckForReport(customButton, signal)

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    const mainPageSuccess = await executeMainPageOperation(customButton, signal)
    if (!mainPageSuccess) {
      logger.warning(`商品卡片${index}主页面操作失败`)
      return false
    }

    // 检查终止信号
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    const goodsListComplete = await waitForGoodsListComplete(signal)
    if (!goodsListComplete) {
      logger.warning(`商品卡片${index}商品列表加载失败`)
      return false
    } else {
      logger.info(`商品卡片${index}商品列表加载成功`)
    }

    const { maxPage } = await getGoodsPageInfo(signal)
    let hasSuccessPage = false

    // 3. 处理每一页
    for (let i = 1; i <= maxPage; i++) {
      // 检查终止信号
      if (signal?.aborted) {
        throw new Error('Operation aborted')
      }

      // 每次翻页都需要重新执行主页面操作
      const pageSuccess = await executeMainPageOperation(customButton, signal)
      if (!pageSuccess) {
        logger.warning(`商品卡片${index}第${i}页主页面操作失败`)
        continue
      }

      // 执行侧边栏操作
      const success = await executeSidebarOperation(index, i, maxPage, signal)
      if (success) {
        hasSuccessPage = true
      }

      // 检查终止信号
      if (signal?.aborted) {
        throw new Error('Operation aborted')
      }

      await waitForSidePanelClosed(signal)
    }

    // 只要有任何一页处理成功，就认为整体成功
    return hasSuccessPage
  } catch (error) {
    if (error instanceof Error && error.message === 'Operation aborted') {
      throw error
    }
    return false
  }
}

/**
 * * 启动提报流程
 * @param customButton - 自定义按钮元素
 */
export async function startReport(customButton: HTMLElement): Promise<void> {
  try {
    // 创建中断信号
    const signal = createAbortController()

    switch (panelState.value.reportMode) {
      case 'select':
        await reportSelectedGoods(signal)
        break
      case 'fullPage':
        await reportFullPage(signal)
        break
      default:
        throw new Error('未知的提报模式')
    }
  } catch (error: any) {
    toast.error(`提报操作错误: ${error?.message || '未知错误'}`)
  }
}

/**
 * * 执行选择模式的提报操作
 * @throws {Error} 当提报失败时抛出错误
 */
async function reportSelectedGoods(signal?: AbortSignal) {
  try {
    // 1. 先进行前置检查
    const sidePanel = document.querySelector(_selectors.drawerWrapper)
    if (sidePanel && getComputedStyle(sidePanel).display !== 'none') {
      const closeSidePanelButton = await waitForElementAppear(
        _selectors.closeSidePanelButton,
        panelState.value.timeout,
        signal,
      )
      if (closeSidePanelButton) {
        closeSidePanelButton.click()
        await smartDelay(
          panelState.value.operationInterval > 0 ? panelState.value.operationInterval : 1,
          signal,
        )
      }
    }

    // 2. 获取所有已收藏的商品卡片
    const collectedButtons = document.querySelectorAll(_selectors.getCollectedGoods)
    if (!collectedButtons.length) {
      logger.warning('未找到已收藏的商品')
      toast.warning('未找到已收藏的商品')
      return
    }

    // 3. 获取对应的商品卡片和提报按钮
    const items = Array.from(collectedButtons)
      .map((button) => {
        // 从收藏按钮向上找到商品卡片容器
        const gridItem = button.closest('.core-grid-item')
        if (!gridItem) return null

        // 在卡片中找到自动提报按钮
        const customButton = gridItem.querySelector('.custom-inject-btn') as HTMLElement
        if (!customButton) return null

        return { gridItem, customButton }
      })
      .filter((item): item is NonNullable<typeof item> => item !== null)

    if (!items.length) {
      logger.warning('未找到可提报的已收藏商品')
      toast.warning('未找到可提报的已收藏商品')
      return
    }

    const startFromIndex = Math.max(1, panelState.value.continueFromIndex) - 1 // 转换为0基索引

    // 显示开始处理的提示
    if (startFromIndex > 0) {
      logger.info(`选择提报：从第${startFromIndex + 1}个已收藏商品开始处理，共${items.length}个商品`)
      toast.info(`从第${startFromIndex + 1}个已收藏商品开始处理`)
    } else {
      logger.info(`选择提报：从第1个已收藏商品开始处理，共${items.length}个商品`)
    }

    let currentIndex = 0

    // 4. 顺序处理每个商品
    for (const { customButton } of items) {
      // 检查终止信号
      if (signal?.aborted) {
        throw new Error('Operation aborted')
      }

      // 跳过指定位置之前的商品
      if (currentIndex < startFromIndex) {
        logger.info(`跳过已收藏商品${currentIndex + 1}`)
        currentIndex++
        continue
      }

      try {
        // 执行提报流程
        const success = await reportOneGoods(customButton, signal)
        if (!success) {
          logger.warning(`商品卡片${currentIndex + 1}提报失败`)
          continue
        }

        // 等待一段时间再处理下一个商品
        if (currentIndex < items.length - 1) {
          await smartDelay(panelState.value.operationInterval, signal)
        }

        currentIndex++
      } catch (error: any) {
        // 如果是终止信号，直接结束
        if (error.message === 'Operation aborted') {
          return
        }
        // 其他错误继续处理下一个
        logger.warning(`商品卡片${currentIndex + 1}处理出错: ${error.message}`)
        currentIndex++
        continue
      }
    }

    toast.success('选择提报完成')
  } catch (error: any) {
    if (error.message === 'Operation aborted') {
      throw error
    }
    logger.warning(`提报操作错误: ${error?.message || '未知错误'}`)
    toast.error(`提报操作错误: ${error?.message || '未知错误'}`)
  }
}

/**
 * * 检测页面加载模式
 * @returns 'pagination' | 'infinite' - 分页模式或无限滚动模式
 */
async function detectPageLoadMode(): Promise<'pagination' | 'infinite'> {
  try {
    // 检测主页面是否存在分页器
    const mainPagination = document.querySelector(_selectors.mainPagePagination)
    if (mainPagination) {
      logger.info('检测到分页器，使用分页模式')
      return 'pagination'
    } else {
      logger.info('未检测到分页器，使用无限滚动模式')
      return 'infinite'
    }
  } catch (error) {
    // 如果检测失败，默认使用分页模式（更安全）
    logger.warning('页面模式检测失败，默认使用分页模式')
    return 'pagination'
  }
}

/**
 * * 滚动到当前内容底部触发加载更多
 * @param signal - 可选的终止信号
 * @returns 是否成功加载了更多内容
 */
async function scrollToLoadMore(signal?: AbortSignal): Promise<boolean> {
  try {
    // 记录滚动前的商品数量
    const beforeCount = document.querySelectorAll(_selectors.gridItemContainer).length
    logger.info(`当前商品数量: ${beforeCount}，尝试加载更多`)

    // 尝试多种滚动策略
    const scrollStrategies = [
      // 策略1: 滚动到最后一个商品
      () => {
        const lastItem = document.querySelector(_selectors.gridItemContainer + ':last-child')
        if (lastItem) {
          lastItem.scrollIntoView({ behavior: 'smooth', block: 'end' })
          logger.info('策略1: 滚动到最后一个商品')
          return true
        }
        return false
      },

      // 策略2: 滚动到页面底部
      () => {
        window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' })
        logger.info('策略2: 滚动到页面底部')
        return true
      },

      // 策略3: 滚动到商品容器底部
      () => {
        const gridContainer = document.querySelector('[data-tid="m4b_tabs_pane"]') ||
                             document.querySelector('.core-tabs-content-item')
        if (gridContainer) {
          gridContainer.scrollTop = gridContainer.scrollHeight
          logger.info('策略3: 滚动到商品容器底部')
          return true
        }
        return false
      }
    ]

    // 尝试每种策略
    for (let strategyIndex = 0; strategyIndex < scrollStrategies.length; strategyIndex++) {
      if (signal?.aborted) throw new Error('Operation aborted')

      const strategy = scrollStrategies[strategyIndex]
      if (!strategy()) continue // 策略不适用，尝试下一个

      // 等待滚动完成
      await smartDelay(1, signal)

      // 检查是否出现加载指示器
      let loadingDetected = false
      for (let i = 0; i < 20; i++) { // 最多等待2秒
        const loadingElement = document.querySelector(_selectors.checkLoading)
        if (loadingElement && getComputedStyle(loadingElement).display !== 'none') {
          loadingDetected = true
          logger.info('检测到加载指示器')
          break
        }
        await smartDelay(0.1, signal)
      }

      // 等待加载完成
      if (loadingDetected) {
        await waitForElementDisappear(_selectors.checkLoading, panelState.value.timeout, signal)
        await smartDelay(0.5, signal) // 额外等待确保DOM更新
      } else {
        // 没有检测到加载指示器，等待一段时间
        await smartDelay(2, signal)
      }

      // 检查是否有新商品加载
      const afterCount = document.querySelectorAll(_selectors.gridItemContainer).length
      if (afterCount > beforeCount) {
        logger.info(`策略${strategyIndex + 1}成功: 商品数量从 ${beforeCount} 增加到 ${afterCount}`)
        return true
      }

      logger.info(`策略${strategyIndex + 1}未加载到新商品，尝试下一个策略`)
    }

    logger.info('所有滚动策略都已尝试，没有更多商品可加载')
    return false

  } catch (error) {
    logger.warning('滚动加载更多失败')
    return false
  }
}

/**
 * * 执行整页提报操作
 * @throws {Error} 当提报失败时抛出错误
 */
async function reportFullPage(signal?: AbortSignal) {
  try {
    // 1. 先进行前置检查
    const sidePanel = document.querySelector(_selectors.drawerWrapper)
    if (sidePanel && getComputedStyle(sidePanel).display !== 'none') {
      const closeSidePanelButton = await waitForElementAppear(
        _selectors.closeSidePanelButton,
        panelState.value.timeout,
        signal,
      )
      if (closeSidePanelButton) {
        closeSidePanelButton.click()
        await smartDelay(
          panelState.value.operationInterval > 0 ? panelState.value.operationInterval : 1,
          signal,
        )
      }
    }

    // 2. 检测页面加载模式
    const loadMode = await detectPageLoadMode()

    let totalCompletedItems = 0
    let batchNumber = 1
    let hasMoreItems = true

    while (hasMoreItems) {
      // 检查终止信号
      if (signal?.aborted) {
        throw new Error('Operation aborted')
      }

      // 获取当前批次的商品卡片
      const items = Array.from(document.querySelectorAll(_selectors.gridItemContainer))
      if (!items.length) {
        logger.warning('未找到可提报的商品')
        if (batchNumber === 1) {
          toast.warning('未找到可提报的商品')
        }
        break
      }

      const startFromIndex = batchNumber === 1 ? Math.max(1, panelState.value.continueFromIndex) - 1 : 0
      const batchItems = items.slice(startFromIndex)

      if (batchNumber === 1) {
        if (startFromIndex > 0) {
          logger.info(`整页提报(${loadMode === 'pagination' ? '分页' : '无限滚动'}模式)：从第${startFromIndex + 1}个商品开始处理`)
          toast.info(`从第${startFromIndex + 1}个商品开始处理`)
        } else {
          logger.info(`整页提报(${loadMode === 'pagination' ? '分页' : '无限滚动'}模式)：开始处理第${batchNumber}批商品，共${batchItems.length}个`)
        }
      } else {
        logger.info(`开始处理第${batchNumber}批商品，共${batchItems.length}个`)
      }

      let batchCompletedItems = 0

      // 处理当前批次的每个商品
      for (let i = 0; i < batchItems.length; i++) {
        // 检查终止信号
        if (signal?.aborted) {
          throw new Error('Operation aborted')
        }

        const item = batchItems[i]
        const customButton = item.querySelector('.custom-inject-btn') as HTMLElement
        if (!customButton) {
          continue
        }

        try {
          // 执行提报流程
          const success = await reportOneGoods(customButton, signal)
          if (success) {
            batchCompletedItems++
            totalCompletedItems++
          } else {
            logger.warning(`第${batchNumber}批第${i + 1}个商品提报失败`)
          }

          // 等待一段时间再处理下一个商品
          if (i < batchItems.length - 1) {
            await smartDelay(
              panelState.value.operationInterval > 0 ? panelState.value.operationInterval : 1,
              signal,
            )
          }
        } catch (error: any) {
          // 如果是终止信号，直接结束
          if (error.message === 'Operation aborted') {
            return
          }
          // 其他错误继续处理下一个
          logger.warning(`第${batchNumber}批第${i + 1}个商品处理出错: ${error.message}`)
          continue
        }
      }

      logger.info(`第${batchNumber}批处理完成，成功处理 ${batchCompletedItems} 个商品`)

      // 根据页面模式决定是否继续
      if (loadMode === 'pagination') {
        // 分页模式：处理完当前页面就结束
        hasMoreItems = false
        logger.info('分页模式：当前页面处理完成')
      } else {
        // 无限滚动模式：尝试加载更多
        logger.info('无限滚动模式：尝试加载下一批商品')
        hasMoreItems = await scrollToLoadMore(signal)
        if (hasMoreItems) {
          batchNumber++
          // 等待一段时间让新商品完全加载
          await smartDelay(1, signal)
        }
      }
    }

    const modeText = loadMode === 'pagination' ? '分页' : '无限滚动'
    toast.success(`整页提报(${modeText}模式)完成，共处理 ${batchNumber} 批，成功提报 ${totalCompletedItems} 个商品`)
    logger.success(`整页提报(${modeText}模式)完成，共处理 ${batchNumber} 批，成功提报 ${totalCompletedItems} 个商品`)
  } catch (error: any) {
    if (error.message === 'Operation aborted') {
      throw error
    }
    logger.warning(`提报操作错误: ${error?.message || '未知错误'}`)
    toast.error(`提报操作错误: ${error?.message || '未知错误'}`)
  }
}
