export const panelStyles = `
.tiktok-panel-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  transition: all 0.3s ease;
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

.tiktok-panel-container.minimized {
  transform: translateY(calc(100% - 4px));
}

.minimize-button {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 20px;
  background: #ffffff;
  border: none;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.minimize-button:hover {
  color: #2196F3;
}

.toolbar-content {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  height: 56px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-right: 1px solid #eee;
  height: 100%;
}

.toolbar-section:last-child {
  border-right: none;
  margin-left: auto;
}

.toolbar-section-title {
  font-size: 13px;
  font-weight: 600;
  margin-right: 12px;
  white-space: nowrap;
}

/* 区域标题颜色 */
.toolbar-section.config .toolbar-section-title {
  color: #3b82f6;
}

.toolbar-section.submit .toolbar-section-title {
  color: #42b883;
}

.toolbar-section.optimize .toolbar-section-title {
  color: #7957d5;
}

.toolbar-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-item label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  user-select: none;
}

.control-item select {
  width: 100px;
  height: 28px;
  padding: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  background: white;
  transition: all 0.3s ease;
}

/* 区域特定的select样式 */
.toolbar-section.config select,
.toolbar-section.config input[type="number"] {
  border-color: #3b82f6;
  background: #eff6ff;
}

.toolbar-section.submit select {
  border-color: #42b883;
  background: #ecfdf5;
}

.toolbar-section.optimize select {
  border-color: #7957d5;
  background: #f5f3ff;
}

.control-item input[type="number"] {
  width: 60px;
  height: 28px;
  padding: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  transition: all 0.3s ease;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #42b883;
}

input:checked + .slider:before {
  transform: translateX(16px);
}

/* 运行按钮样式 */
.run-button {
  height: 28px;
  padding: 0 12px;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

/* 区域特定的按钮样式 */
.toolbar-section.submit .run-button {
  background: #42b883;
}

.toolbar-section.submit .run-button:hover {
  background: #34a873;
}

.toolbar-section.optimize .run-button {
  background: #7957d5;
}

.toolbar-section.optimize .run-button:hover {
  background: #6a4bc0;
}

.run-button.running {
  background: #ef4444 !important;
}

.run-button.running:hover {
  background: #dc2626 !important;
}

.button-icon {
  font-size: 14px;
}

/* 信息区域样式 */
.wechat-info {
  font-size: 12px;
  color: #666;
  margin-right: 12px;
  white-space: nowrap;
}

/* 日志按钮容器 */
.log-button-container {
  position: relative;
}

/* 日志按钮样式 */
.log-button {
  height: 28px;
  padding: 0 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.log-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}

.log-button.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #fff;
}

.log-button-icon {
  font-size: 14px;
}

/* 日志面板样式 */
.log-panel {
  position: fixed;
  left: 0;
  top: 100%;
  height: 300px;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999998;
  padding: 16px;
  display: flex;
  flex-direction: column;
  margin: 0;
  border: none;
  will-change: top;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  transition: top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.log-panel.visible {
  top: calc(100% - 356px); /* 300px height + 56px bottom margin */
}

.log-panel * {
  pointer-events: auto;
}

.log-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.log-panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.log-panel-content {
  flex: 1;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.5;
  color: #666;
  padding-right: 8px;
}

.log-panel-content::-webkit-scrollbar {
  width: 6px;
}

.log-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.log-panel-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.log-panel-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 日志条目样式 */
.log-entry {
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  background: #fafafa;
  border-left: 4px solid transparent;
}

.log-entry.info {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.log-entry.error {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.log-entry.success {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.log-entry.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.log-entry.debug {
  border-left-color: #7957d5;
  background: #f5f3ff;
}

.log-timestamp {
  color: #666;
  font-size: 12px;
  margin-right: 8px;
}

.log-message {
  color: #333;
  word-break: break-all;
}

/* 清除按钮样式 */
.clear-button {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.clear-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #eff6ff;
}
`
