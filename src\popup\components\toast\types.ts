export type ToastType = 'success' | 'error' | 'info' | 'warning'

export interface ToastOptions {
  message: string
  type?: ToastType
  duration?: number
  onClose?: () => void
}

export interface ToastInstance {
  id: number
  message: string
  type: ToastType
  duration: number
  onClose?: () => void
}

export interface ToastApi {
  show: (options: ToastOptions) => void
  success: (message: string, duration?: number) => void
  error: (message: string, duration?: number) => void
  info: (message: string, duration?: number) => void
  warning: (message: string, duration?: number) => void
  clear: () => void
}
