import { checkAuth } from '../../utils/auth'
import { setupUI } from './ui'
import { delay } from '../utils/utils'
import { createButtonInjectionObserver, createMainObserver, createShopObserver } from '../dom/observers'
import { queryMachineCode, querySecret, queryShopCode, queryStartTime, saveStartTime } from '../../utils/storage'
import { toast } from '../components/toast'
import {
  checkShopInfoOpened,
  closeShopInfo,
  currentShopCode,
  getShopCode,
  openShopInfo,
  updateShopCode,
} from '../dom/actions'
import { buttonService } from '../services/buttonService'
import { effect } from '../utils/reactive'
import { messageBox } from '../components/messageBox'
import { authPanel } from '../components/AuthPanel'

const AUTH_TYPE = import.meta.env.VITE_AUTH_TYPE || 'SHOP'

enum Mode {
  TRIAL_SHOP = 'TRIAL_SHOP',
  TRIAL_MACHINE = 'TRIAL_MACHINE',
  SHOP = 'SHOP',
  MACHINE = 'MACHINE',
}

// 创建店铺码监听器
function createShopCodeWatcher() {
  return effect(async () => {
    const code = currentShopCode.value
    const isShopCodeExist = await queryShopCode()
    const secret = await querySecret()
    const startTime = await queryStartTime()

    if (!isShopCodeExist && code) {
      updateShopCode(code)
      toast.info('当前店铺码已保存')
      return
    }

    if (!code) {
      return
    }

    // 如果有授权密钥，验证授权状态
    if (secret) {
      try {
        const auth = await checkAuth(secret, { currentShopCode: code })
        if (auth.isValid) {
          if (isShopCodeExist === code) {
            toast.success(`店铺 ${code} 已授权`)
          }
        }
      } catch (error) {

      }
      return
    }

    // 试用模式下的店铺码匹配
    if (isShopCodeExist && code && isShopCodeExist === code) {
      if (startTime) {
        const now = Date.now()
        const start = parseInt(startTime)
        const trialDuration = 2 * 60 * 1000 // 2分钟
        const remainingTime = Math.max(0, trialDuration - (now - start))
        if (remainingTime > 0) {
          const remainingSeconds = Math.ceil(remainingTime / 1000)
          toast.info(`试用模式：店铺 ${code} 剩余${remainingSeconds}秒`)
        }
      } else {
        toast.info(`试用模式：店铺 ${code}`)
      }
    }
  })
}

async function shopMode() {
  createShopObserver(
    checkShopInfoOpened,
    openShopInfo,
    getShopCode,
    closeShopInfo,
    delay,
    (code: string) => {
      currentShopCode.value = code
    },
  )

  // 创建店铺码监听
  createShopCodeWatcher()
}

async function machineMode() {}

async function trialShopMode() {
  // 检查是否已有起始时间
  const startTime = await queryStartTime()
  if (!startTime) {
    // 如果没有起始时间，先不设置，等第一次点击时设置
    toast.info('试用模式：2分钟')
  } else {
    // 如果已有起始时间，检查是否在试用期内
    const now = Date.now()
    const start = parseInt(startTime)
    const trialDuration = 2 * 60 * 1000 // 2分钟
    const remainingTime = Math.max(0, trialDuration - (now - start))
    const remainingSeconds = Math.ceil(remainingTime / 1000)

    if (remainingTime > 0) {
      toast.info(`试用模式：剩余${remainingSeconds}秒`)
    } else {
      await messageBox.error(
        '试用期已结束，请完成授权后继续使用',
        true,
        async () => {
          await authPanel.show()
        }
      )
    }
  }

  createShopObserver(
    checkShopInfoOpened,
    openShopInfo,
    getShopCode,
    closeShopInfo,
    delay,
    (code: string) => {
      currentShopCode.value = code
    },
  )

  // 创建店铺码监听
  createShopCodeWatcher()
}

async function trialMachineMode() {
  // 检查是否已有起始时间
  const startTime = await queryStartTime()
  if (!startTime) {
    // 如果没有起始时间，先不设置，等第一次点击时设置
    toast.info('试用模式：2分钟')
  } else {
    // 如果已有起始时间，检查是否在试用期内
    const now = Date.now()
    const start = parseInt(startTime)
    const trialDuration = 2 * 60 * 1000 // 2分钟
    const remainingTime = Math.max(0, trialDuration - (now - start))
    const remainingSeconds = Math.ceil(remainingTime / 1000)

    if (remainingTime > 0) {
      toast.info(`试用模式：剩余${remainingSeconds}秒`)
    } else {
      await messageBox.error(
        '试用期已结束，请完成授权后继续使用',
        true,
        async () => {
          await authPanel.show()
        }
      )
    }
  }
}

async function getMode(): Promise<Mode> {
  const savedShopCode = await queryShopCode()
  const savedMachineCode = await queryMachineCode()
  const savedSecret = await querySecret()

  if (!savedSecret) {
    return AUTH_TYPE === 'SHOP' ? Mode.TRIAL_SHOP : Mode.TRIAL_MACHINE
  }

  try {
    const options = {
      ...(savedShopCode && { currentShopCode: savedShopCode }),
      ...(savedMachineCode && { currentMachineCode: savedMachineCode })
    }

    const auth = await checkAuth(savedSecret, options)

    if (auth.isValid) {
      return AUTH_TYPE === 'SHOP' ? Mode.SHOP : Mode.MACHINE
    }
  } catch (error) {

  }

  return AUTH_TYPE === 'SHOP' ? Mode.TRIAL_SHOP : Mode.TRIAL_MACHINE
}

async function main() {
  const mode = await getMode()
  await setupUI()

  // 如果是商铺模式（包括试用），总是启用商铺码监听
  if (mode === Mode.TRIAL_SHOP || mode === Mode.SHOP) {
    createShopObserver(
      checkShopInfoOpened,
      openShopInfo,
      getShopCode,
      closeShopInfo,
      delay,
      (code: string) => {
        currentShopCode.value = code
      },
    )
    createShopCodeWatcher()
  }

  // 根据模式初始化其他功能
  switch (mode) {
    case Mode.TRIAL_SHOP:
      await trialShopMode()
      break
    case Mode.TRIAL_MACHINE:
      await trialMachineMode()
      break
    case Mode.SHOP:
      await shopMode()
      break
    case Mode.MACHINE:
      await machineMode()
      break
  }
}

createMainObserver(main)
createButtonInjectionObserver(buttonService.injectCustomButton.bind(buttonService))
