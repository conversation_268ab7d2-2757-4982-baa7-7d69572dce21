<template>
  <div class="fixed inset-x-0 top-4 mx-auto flex flex-col items-center gap-2 z-50 pointer-events-none">
    <TransitionGroup
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="transform translate-y-2 opacity-0"
      enter-to-class="transform translate-y-0 opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="transform translate-y-0 opacity-100"
      leave-to-class="transform translate-y-2 opacity-0"
      move-class="transition-all duration-300 ease-in-out"
    >
      <div
        v-for="(toast, index) in toasts"
        :key="toast.id"
        class="transform transition-all duration-300 flex justify-center"
        :style="{
          transform: `translateY(${index * 6}px)`,
        }"
      >
        <ToastItem
          :message="toast.message"
          :type="toast.type"
        />
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ToastItem from './ToastItem.vue'
import type { ToastInstance, ToastOptions } from './types'

const MAX_COUNT = 5 // 最大显示数量
const DEFAULT_DURATION = 3000 // 默认显示时间

const toasts = ref<ToastInstance[]>([])
let currentId = 0

const removeToast = (id: number) => {
  const index = toasts.value.findIndex(t => t.id === id)
  if (index > -1) {
    const toast = toasts.value[index]
    toasts.value.splice(index, 1)
    toast.onClose?.()
  }
}

const show = (options: ToastOptions) => {
  const id = currentId++
  const toast: ToastInstance = {
    id,
    message: options.message,
    type: options.type || 'info',
    duration: options.duration || DEFAULT_DURATION,
    onClose: options.onClose
  }

  // 如果超过最大数量，移除最早的
  if (toasts.value.length >= MAX_COUNT) {
    toasts.value.shift()
  }

  toasts.value.push(toast)

  // 设置定时移除
  setTimeout(() => {
    removeToast(id)
  }, toast.duration)
}

const success = (message: string, duration?: number) => {
  show({ message, type: 'success', duration })
}

const error = (message: string, duration?: number) => {
  show({ message, type: 'error', duration })
}

const info = (message: string, duration?: number) => {
  show({ message, type: 'info', duration })
}

const warning = (message: string, duration?: number) => {
  show({ message, type: 'warning', duration })
}

const clear = () => {
  toasts.value = []
}

defineExpose({
  show,
  success,
  error,
  info,
  warning,
  clear
})
</script> 
