import { _selectors } from './selectors'
import { clickElementWithDelay, delay } from '../utils/utils'
import {  saveShopCode } from '../../utils/storage'
import { ref } from '../utils/reactive'
import { buttonService } from '../services/buttonService'





// 使用 ref 替代 reactive
export const currentShopCode = ref<string | null>(null)

export function checkShopInfoOpened(): boolean {
  try {
    const shopInfoOpened = document.querySelector(_selectors.checkShopInfoOpened) as HTMLElement
    return !!shopInfoOpened
  } catch (error: any) {
    return false
  }
}

export async function openShopInfo() {
  try {
    await clickElementWithDelay(_selectors.openShopInfo, 10, 2)
  } catch (error) {
    throw new Error('打开店铺信息失败')
  }
}

export async function closeShopInfo() {
  try {
    await clickElementWithDelay(_selectors.openShopInfo, 10, 2)
  } catch (error) {
    throw new Error('关闭店铺信息失败')
  }
}

export function getShopCode(): string {
  const shopCodeElement = document.querySelector(_selectors.getShopCode)
  if (!shopCodeElement) {
    throw new Error('未找到店铺码元素')
  }

  const content = shopCodeElement.textContent?.trim() || ''
  let textArray = content.split(':')
  let text = textArray[textArray.length - 1].trim()
  const match = text.match(/^[A-Z0-9]+/)
  text = match ? match[0] : ''

  if (!text) {
    throw new Error('无法解析店铺代码')
  }
  return text
}

export async function updateShopCode(shopCode: string): Promise<void> {
  try {
    await saveShopCode(shopCode)
    currentShopCode.value = shopCode
    chrome.runtime.sendMessage({ type: 'SHOP_CODE_UPDATED', shopCode })
  } catch (error: any) {
    throw error
  }
}

// 导出 buttonService 的方法
export const injectCustomButton = buttonService.injectCustomButton.bind(buttonService)
