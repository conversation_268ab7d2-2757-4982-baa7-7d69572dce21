import { ref } from '../utils/reactive'
import { PanelState } from '../types/types'
import { savePanelConfig, queryPanelConfig } from '../../utils/storage'
import { logger } from '../utils/logger'


// 验证时间间隔是否在有效范围内
function validateTimeInterval(value: number, defaultValue: number = 0): number {
  const MIN_INTERVAL = 0

  if (typeof value !== 'number' || isNaN(value)) {
    return defaultValue
  }

  return Math.max(value, MIN_INTERVAL)
}

// 获取日志状态
export const logState = logger.getState()

export const panelState = ref<PanelState>({
  reportMode: 'fullPage',
  operationInterval: 0.3,
  isRunning: false,
  optimizeMode: 'fullPage',
  isOptimizing: false,
  isPanelVisible: true,
  isPanelMinimized: false,
  reportUseKeywords: true,
  selectStrategy: 'first',
  customStartIndex: 1,
  timeout: 5,
  isControlPanelVisible: true,
  continueFromIndex: 1,
})

// 保存面板配置
export async function savePanelSettings() {
  await savePanelConfig({
    reportMode: panelState.value.reportMode,
    operationInterval: validateTimeInterval(panelState.value.operationInterval),
    optimizeMode: panelState.value.optimizeMode,
    reportUseKeywords: panelState.value.reportUseKeywords,
    selectStrategy: panelState.value.selectStrategy,
    customStartIndex: panelState.value.customStartIndex,
    timeout: validateTimeInterval(panelState.value.timeout),
    isLogPanelVisible: logState.value.isVisible,
    isControlPanelVisible: panelState.value.isControlPanelVisible,
    isPanelMinimized: panelState.value.isPanelMinimized,
    continueFromIndex: panelState.value.continueFromIndex
  })
}

// 加载面板配置
export async function loadPanelSettings() {
  const config = await queryPanelConfig()
  if (config) {
    // 更新面板状态，使用默认值作为回退
    panelState.value = {
      ...panelState.value, // 保留当前运行时状态（isRunning等）
      reportMode: config.reportMode ?? panelState.value.reportMode,
      operationInterval: validateTimeInterval(config.operationInterval),
      optimizeMode: config.optimizeMode ?? panelState.value.optimizeMode,
      reportUseKeywords: config.reportUseKeywords ?? true,
      selectStrategy: config.selectStrategy ?? 'custom',
      customStartIndex: config.customStartIndex ?? 1,
      timeout: validateTimeInterval(config.timeout, 5),
      isControlPanelVisible: config.isControlPanelVisible ?? true,
      isPanelMinimized: config.isPanelMinimized ?? false,
      continueFromIndex: config.continueFromIndex ?? 1,
      // 保持这些运行时状态不变
      isRunning: panelState.value.isRunning,
      isOptimizing: panelState.value.isOptimizing,
      isPanelVisible: panelState.value.isPanelVisible
    }

    // 设置日志面板可见性
    logger.setVisibility(config.isLogPanelVisible ?? false)
  }
}
