

// 存储键名
const STORAGE_KEYS = {
  AUTH_KEY: 'tiktok_seller_auth_key',
  AUTH_SECRET: 'tiktok_seller_auth_secret',
  MACHINE_CODE: 'tiktok_seller_machine_code',
  SHOP_CODE: 'tiktok_seller_shop_code',
  START_TIME: 'tiktok_seller_start_time',
  PANEL_CONFIG: 'tiktok_seller_panel_config',
} as const



interface PanelConfig {
  reportMode: 'select' | 'fullPage'
  operationInterval: number
  optimizeMode: 'fullPage' | 'autoPage'
  reportUseKeywords: boolean
  selectStrategy: 'random' | 'first' | 'custom'
  customStartIndex: number
  timeout: number
  isLogPanelVisible: boolean
  isControlPanelVisible: boolean
  isPanelMinimized: boolean
  continueFromIndex?: number
}

// 存储授权信息
export async function saveKey(key: string): Promise<void> {
  await chrome.storage.local.set({ [STORAGE_KEYS.AUTH_KEY]: key })
}

// 获取授权信息
export async function queryKey(): Promise<string | null> {
  const result = await chrome.storage.local.get(STORAGE_KEYS.AUTH_KEY)
  return result[STORAGE_KEYS.AUTH_KEY] || null
}

export async function saveSecret(secret: string): Promise<void> {
  await chrome.storage.local.set({ [STORAGE_KEYS.AUTH_SECRET]: secret })
}

export async function querySecret(): Promise<string | null> {
  const result = await chrome.storage.local.get(STORAGE_KEYS.AUTH_SECRET)
  return result[STORAGE_KEYS.AUTH_SECRET] || null
}

// 存储机器码
export async function saveMachineCode(code: string): Promise<void> {
  await chrome.storage.local.set({ [STORAGE_KEYS.MACHINE_CODE]: code })
}

// 获取机器码
export async function queryMachineCode(): Promise<string | null> {
  const result = await chrome.storage.local.get(STORAGE_KEYS.MACHINE_CODE)
  return result[STORAGE_KEYS.MACHINE_CODE] || null
}

// 存储店铺码
export async function saveShopCode(code: string): Promise<void> {
  await chrome.storage.local.set({ [STORAGE_KEYS.SHOP_CODE]: code })
}

// 获取店铺码
export async function queryShopCode(): Promise<string | null> {
  const result = await chrome.storage.local.get(STORAGE_KEYS.SHOP_CODE)
  return result[STORAGE_KEYS.SHOP_CODE] || null
}

// 清除所有授权相关信息
export async function clearAuthInfo(): Promise<void> {
  await chrome.storage.local.remove([STORAGE_KEYS.AUTH_KEY, STORAGE_KEYS.AUTH_SECRET, STORAGE_KEYS.MACHINE_CODE, STORAGE_KEYS.SHOP_CODE])
}

export async function saveStartTime(startTime: string): Promise<void> {
  await chrome.storage.local.set({ [STORAGE_KEYS.START_TIME]: startTime })
}

export async function queryStartTime(): Promise<string | null> {
  const result = await chrome.storage.local.get(STORAGE_KEYS.START_TIME)
  return result[STORAGE_KEYS.START_TIME] || null
}

// 保存面板配置
export async function savePanelConfig(config: PanelConfig): Promise<void> {
  await chrome.storage.local.set({ panelConfig: config })
}

// 获取面板配置
export async function queryPanelConfig(): Promise<PanelConfig | null> {
  const result = await chrome.storage.local.get('panelConfig')
  return result.panelConfig || null
}

