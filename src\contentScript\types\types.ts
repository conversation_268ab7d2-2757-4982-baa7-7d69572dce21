// Panel related types and state
export interface PanelState {
  reportMode: 'select' | 'fullPage'
  /** 操作间隔时间，范围：0-300秒 */
  operationInterval: number
  isRunning: boolean
  optimizeMode: 'fullPage' | 'autoPage'
  isOptimizing: boolean
  isPanelVisible: boolean
  /** 面板是否最小化 */
  isPanelMinimized: boolean
  reportUseKeywords: boolean
  selectStrategy: 'random' | 'first' | 'custom'
  customStartIndex: number
  /** 翻页间隔，范围：0-300秒 */
  timeout: number
  isControlPanelVisible: boolean
  /** 从第几个商品开始继续处理 */
  continueFromIndex: number
}

export interface LogEntry {
  type: string
  message: string
  timestamp: number
}
