import { querySecret, queryMachineCode, saveMachineCode, queryShopCode, queryStartTime } from './storage'


const AUTH_TYPE = import.meta.env.VITE_AUTH_TYPE || 'SHOP'

// 授权信息接口
interface AuthInfo {
  // 授权类型
  identifier: string // 标识符(店铺码或机器码)
  expireTime: Date // 过期时间
}

interface VerifyAuthInfo extends AuthInfo {
  isValid: boolean
  error?: string
}


// 生成机器码
export async function generateMachineCode(): Promise<string> {
  try {
    const response = await chrome.runtime.sendMessage({ type: 'GENERATE_MACHINE_CODE' })
    if (response.success) {
      return response.machineCode
    } else {

      return ''
    }
  } catch (error) {

    return ''
  }
}

// 验证授权码
export function checkAuth(
  authSecret: string,
  options?: {
    currentShopCode?: string
    currentMachineCode?: string
  },
): Promise<VerifyAuthInfo> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(
      {
        type: 'CHECK_AUTH',
        authSecret,
        options
      },
      (response) => {
        resolve(response)
      }
    )
  })
}

// 格式化过期时间
export function formatExpireTime(date: Date | string): string {
  // 确保输入是 Date 对象
  const expireDate = date instanceof Date ? date : new Date(date)

  // 使用 toLocaleString 格式化日期和时间
  return expireDate.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false // 使用24小时制
  })
}

// 鉴权守卫函数
export async function authGuard(): Promise<boolean> {
  try {
    const secret = await querySecret()
    const machineCode = await queryMachineCode()
    const shopCode = await queryShopCode()
    const startTime = await queryStartTime()

    // 如果有授权密钥，验证授权
    if (secret) {
      const options = {
        currentMachineCode: machineCode || undefined,
        ...(shopCode && AUTH_TYPE === 'SHOP' ? { currentShopCode: shopCode } : {})
      }

      const auth = await checkAuth(secret, options)
      return auth.isValid
    }

    // 如果没有 startTime，默认可用
    if (!startTime) {
      return true
    }

    // 只有在有 startTime 的情况下才检查试用期
    const trialDuration = 2 * 60 * 1000 // 2分钟
    const now = Date.now()
    const start = parseInt(startTime)

    // 只有在确实超过试用期时才返回 false
    if (now - start > trialDuration) {

      return false
    }

    return true
  } catch (error) {
    // 出错时也默认可用

    return true
  }
}

/**
 * 获取身份码
 * @param authType 授权类型 ('MACHINE' | 'SHOP')
 * @returns 返回身份码或空字符串（如果无法获取）
 */
export async function getIdentityCode(authType: 'MACHINE' | 'SHOP' = 'SHOP'): Promise<string> {
  try {
    // 获取或生成机器码（必须有）
    let machineCode = await queryMachineCode()
    if (!machineCode) {
      machineCode = await generateMachineCode()
      if (machineCode) {
        await saveMachineCode(machineCode)
      } else {

        return ''
      }
    }

    // 验证机器码格式
    if (!machineCode || !/^[A-Za-z0-9-]{1,64}$/.test(machineCode)) {

      return ''
    }

    // 机器授权模式
    if (authType === 'MACHINE') {
      return machineCode
    }

    // 商铺授权模式：必须同时有机器码和店铺码
    const shopCode = await queryShopCode()

    // 验证店铺码
    if (!shopCode || !/^[A-Za-z0-9]{1,32}$/.test(shopCode)) {

      return ''
    }

    // 返回完整的身份码
    return `${machineCode}-${shopCode}`
  } catch (error) {

    return ''
  }
}
