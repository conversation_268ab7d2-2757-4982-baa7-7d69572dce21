/**
 * 商品选择策略服务
 */
export function selectItems(items: NodeListOf<Element>, strategy: 'random' | 'first' | 'custom' | 'all', startIndex: number = 1): Element[] {
  const itemsArray = Array.from(items)
  const maxItems = 5 // 每页最多选择5个商品

  switch (strategy) {
    case 'all':
      return itemsArray
    case 'random':
      // 随机选择5个商品
      const shuffled = itemsArray.sort(() => 0.5 - Math.random())
      return shuffled.slice(0, maxItems)

    case 'first':
      // 选择前5个商品
      return itemsArray.slice(0, maxItems)

    case 'custom':
      // 从指定位置开始选择5个商品
      const start = Math.max(0, startIndex - 1) // 转换为0基索引
      return itemsArray.slice(start, start + maxItems)
  }
}
