import { _selectors } from '../dom/selectors'
import { logger } from '../utils/logger'
import { toast } from '../components/toast'
import { reportOneGoods } from './reportService'
import { authGuard } from '../../utils/auth'
import { messageBox } from '../components/messageBox'
import { authPanel } from '../components/AuthPanel'

interface CustomButton extends HTMLButtonElement {
  originalButton?: HTMLElement
}

export class ButtonService {

  private getTargetButtons(): NodeListOf<HTMLElement> {
    return document.querySelectorAll(_selectors.getReportButtonGrid) as NodeListOf<HTMLElement>
  }

  public async injectCustomButton() {
    try {
      // 检查授权状态
      const targetButtons = this.getTargetButtons()
      if (!targetButtons.length) {
        return
      }



      targetButtons.forEach((button,index) => {
        // 多重检查防止重复注入
        if (
          button.id || // 检查是否已经有ID（说明已处理过）
          button.previousElementSibling?.classList.contains(
            _selectors.customInjectButton.replace('.', ''),
          )
        ) {
          return
        }

        const customButton = document.createElement('button') as CustomButton
        customButton.className = _selectors.customInjectButton.replace('.', '')

        customButton.type = 'button'
        customButton.style.backgroundColor = '#ff005c'
        customButton.style.borderRadius = '4px'

        // 为原始按钮设置唯一ID
        const reportBtnId = `report-btn-${index+1}`
        button.id = reportBtnId
        customButton.setAttribute('data-report-btn-id', reportBtnId)
        customButton.originalButton = button

        // 栅格布局：按钮占满宽度，垂直排列
        customButton.style.display = 'block'
        customButton.style.width = '100%'


        // 调整原始按钮样式
        button.style.display = 'block'
        button.style.width = '100%'

        const span = document.createElement('span')
        span.textContent = `${index+1}-自动提报`


        span.style.fontWeight = 'bold'
        span.style.color = '#fff'

        customButton.appendChild(span)

        // 统一将自定义按钮插入到原始按钮前面
        button.parentNode?.insertBefore(customButton, button)

        customButton.addEventListener('click', async () => {
          try {
            // 每次点击时检查授权状态
            const isAuthorized = await authGuard()
            if (!isAuthorized) {
              await messageBox.error('请先完成授权后再使用此功能', true)
              await authPanel.show()
              return
            }

            await reportOneGoods(customButton)
          } catch (error: any) {
            logger.error('执行自动提报时遇到问题，请稍后重试')
            toast.error('执行自动提报时遇到问题，请稍后重试')
          }
        })
      })
    } catch (error: any) {
      logger.error('添加自动提报按钮时出现问题，请刷新页面重试')
    }
  }


}

// 导出单例
export const buttonService = new ButtonService()
