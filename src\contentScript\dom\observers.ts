import { _selectors } from './selectors'
import { logger } from '../utils/logger'
import { ref } from '../utils/reactive'

// 创建全局状态控制执行
const observerState = {
  shopObserverExecuted: ref(false),
  uiObserverExecuted: ref(false)
}

export interface ObserverConfig {
  // 执行的动作
  action: () => Promise<void>;
  // 检查条件的函数
  checkCondition?: (mutation: MutationRecord) => boolean;
  // 是否在条件满足时断开连接
  disconnectOnSuccess?: boolean;
  // 自定义错误消息
  errorMessage?: string;
  // 是否执行初始检查
  initialCheck?: boolean;
  // 观察器配置
  observerOptions?: MutationObserverInit;
}
/**
 * 创建通用的 MutationObserver
 * @param config 观察器配置
 * @param config.action 观察器执行的函数
 * @param config.checkCondition 观察器检查的条件 如果为true则执行action
 * @param config.disconnectOnSuccess 观察器在成功后是否断开连接
 * @param config.errorMessage 观察器错误信息
 * @param config.initialCheck 观察器是否在初始化时执行
 * @param config.observerOptions 观察器选项
 * @returns MutationObserver 实例
 */
function createObserver(config: ObserverConfig): MutationObserver {
  const {
    action,
    checkCondition,
    disconnectOnSuccess = false,
    initialCheck = false,
    observerOptions = {
      childList: true,
      subtree: true,
    },
  } = config

  const observer = new MutationObserver(async (mutations) => {
    try {
      let shouldExecute = true

      if (checkCondition) {
        shouldExecute = mutations.some((mutation) => checkCondition(mutation))
      }

      if (shouldExecute) {
        // 如果需要在成功时断开连接，立即断开
        if (disconnectOnSuccess) {
          observer.disconnect()
        }
        await action()
      }
    } catch (error) {

    }
  })

  observer.observe(document.body, observerOptions)

  // 如果需要初始检查，立即执行一次
  if (initialCheck) {
    const shouldExecute = checkCondition ? checkCondition({} as MutationRecord) : true
    if (shouldExecute) {
      if (disconnectOnSuccess) {
        observer.disconnect()
      }
    }
  }

  return observer
}

/**
 * 创建店铺观察器
 * @param checkShopInfoOpened 检查店铺信息是否打开的函数
 * @param openShopInfo 打开店铺信息的函数
 * @param getShopCode 获取店铺码的函数
 * @param closeShopInfo 关闭店铺信息的函数
 * @param delay 延迟函数
 * @param onShopCodeUpdate 店铺码更新回调
 * @returns MutationObserver 实例
 */
export function createShopObserver(
  checkShopInfoOpened: () => boolean,
  openShopInfo: () => void,
  getShopCode: () => string,
  closeShopInfo: () => void,
  delay: (seconds: number) => Promise<void>,
  onShopCodeUpdate: (code: string) => void,
): MutationObserver {
  let isExecuting = false
  let wasInitiallyOpened = false

  return createObserver({
    action: async () => {
      if (isExecuting || observerState.shopObserverExecuted.value) return

      try {
        isExecuting = true
        wasInitiallyOpened = checkShopInfoOpened()

        // 只在面板未打开时才打开
        if (!wasInitiallyOpened) {
          openShopInfo()
          // 等待面板完全打开
          await delay(0.5)
        }

        const code = getShopCode()
        onShopCodeUpdate(code)
        observerState.shopObserverExecuted.value = true

        // 只有在我们主动打开面板的情况下才关闭它
        if (!wasInitiallyOpened) {
          await delay(0.5)
          closeShopInfo()
        }
      } catch (error) {

      } finally {
        isExecuting = false
      }
    },
    checkCondition: (mutation) => {
      if (observerState.shopObserverExecuted.value) return false
      return Boolean(document.querySelector(_selectors.checkInitAvailable))
    },
    disconnectOnSuccess: true,
  })
}

export function createButtonInjectionObserver(action: () => Promise<void>) {
  return createObserver({
    action,
    checkCondition: (mutation) => {
      // 只检查新增的节点
      return Array.from(mutation.addedNodes).some((node: Node) => {
        if (node instanceof Element) {
          // 检查是否有新的提报按钮且没有对应的自定义按钮
          const gridButtons = node.matches(_selectors.getReportButtonGrid)
            ? [node]
            : Array.from(node.querySelectorAll(_selectors.getReportButtonGrid))

          return gridButtons.some(button =>
            !button.id && // 检查是否没有ID（说明未处理过）
            !button.previousElementSibling?.classList.contains(_selectors.customInjectButton.replace('.', ''))
          )
        }
        return false
      })
    },
    initialCheck: true,
  })
}

export function createMainObserver(action: () => Promise<void>) {
  return createObserver({
    action,
    checkCondition: (mutation) =>{
      return Boolean(document.querySelector(_selectors.checkInitAvailable))
    },
    disconnectOnSuccess: true,
  })
}

/**
 * 创建 UI 观察器
 * @param loadPanelSettings 加载面板设置的函数
 * @param createPanel 创建面板的函数
 * @returns Promise<MutationObserver>
 */
export function createUIObserver(
  loadPanelSettings: () => Promise<void>,
  createPanel: () => void,
): Promise<void> {
  return new Promise((resolve) => {
    createObserver({
      action: async () => {
        if (observerState.uiObserverExecuted.value) return
        observerState.uiObserverExecuted.value = true

        await loadPanelSettings()
        createPanel()
        resolve()
      },
      checkCondition: (mutation) => {
        if (observerState.uiObserverExecuted.value) return false
        return Boolean(document.querySelector(_selectors.checkInitAvailable))
      },
      disconnectOnSuccess: true,
      initialCheck: true,
    })
  })
}






