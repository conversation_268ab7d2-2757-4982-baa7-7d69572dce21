# auto-tiktok-seller

> TikTok Shop商品自动提报Chrome扩展，基于 Vite + Vue + Manifest v3 构建

## 功能特点

- 支持多种提报模式：
  - 选择模式：仅提报已收藏的商品
  - 整页模式：提报当前页面所有商品
- 灵活的选品策略：
  - 从头选择：从第一个商品开始
  - 随机选择：随机选择商品
  - 自定义起始：从指定位置开始选择
- 智能操作控制：
  - 可配置操作间隔
  - 可配置翻页间隔
  - 支持关键词推荐优化
- 完整的状态管理：
  - 任务进度跟踪
  - 错误处理和恢复
  - 详细的操作日志
- 友好的用户界面：
  - 浮动控制面板
  - 实时进度显示
  - 操作日志查看

## 环境变量配置

本项目使用环境变量来管理敏感配置。在开始开发之前，请按照以下步骤配置环境变量：

1. 在项目根目录创建以下文件之一：
   - `.env.development` (开发环境)
   - `.env.production` (生产环境)
   - `.env.local` (本地开发，优先级最高)

2. 在环境变量文件中设置以下必需的环境变量：

   ```
   # 应用密钥 - 用于生成授权码的加密密钥
   VITE_APP_SECRET=your-secret-key-here
   ```

   注意：请使用足够复杂的密钥来确保安全性。

3. 确保环境变量文件已被 .gitignore 忽略，不要提交到代码库中。

## 安装说明

1. 确保 Node.js 版本 >= 14
2. 在 `src/manifest.ts` 中配置扩展名称和其他信息
3. 运行 `npm install` 安装依赖

## 开发模式

### Chrome扩展开发模式

1. 运行开发服务器：

   ```shell
   cd auto-tiktok-seller
   npm run dev
   ```

2. 在Chrome浏览器中：
   - 打开扩展管理页面
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `auto-tiktok-seller/build` 目录

### 普通前端开发模式

1. 访问 `http://0.0.0.0:3000/`
2. 调试弹出页面：访问 `http://0.0.0.0:3000/popup.html`
3. 调试选项页面：访问 `http://0.0.0.0:3000/options.html`

## 自动化流程说明

### 页面结构

1. 主页面：显示商品列表，每个商品有"添加商品"按钮
2. 侧边栏页面：点击"添加商品"后打开
3. 商品选择页面：在侧边栏中点击"选择商品"按钮后显示

### 按钮结构

```html
<div class="core-btn-group">
   <!-- 注入的自动化按钮 -->
   <button class="custom-inject-btn" data-report-btn-id="report-btn-xxx">自动提报</button>
   <!-- TikTok Shop原生按钮 -->
   <button id="report-btn-xxx">添加商品</button>
</div>
```

### 自动化流程

1. 初始化流程：
   - 页面加载时注入自动提报按钮
   - 监听页面变化，动态注入新按钮
   - 初始化面板状态和配置

2. 提报流程：

   ```mermaid
   graph TD
       A[点击自动提报按钮] --> B[创建任务]
       B --> C[执行主页面操作]
       C --> D[点击原始添加商品按钮]
       D --> E[等待侧边栏打开]
       E --> F[点击选择商品按钮]
       F --> G[选择商品]
       G --> H[处理关键词推荐]
       H --> I[提交并关闭]
       I --> J[检查是否继续下一页]
       J -->|是| D
       J -->|否| K[完成]
   ```

3. 任务状态流转：
   - idle: 初始状态
   - running: 运行中
   - waiting_for_sidebar: 等待侧边栏
   - sidebar_opened: 侧边栏已打开
   - selecting_items: 选择商品中
   - completed: 完成
   - error: 错误
   - terminated: 终止

4. 页码处理特性：
   - 每次进入侧边栏时页码重置为1
   - 维护独立的页码计数器
   - 需要手动跳转到目标页码

## 打包发布

运行以下命令进行打包：

```shell
npm run build
```

打包后的文件位于 `build` 目录，可以提交到 Chrome Web Store。
发布指南请参考：[Chrome Web Store官方指南](https://developer.chrome.com/webstore/publish)

## 开发者注意事项

1. 时间单位统一：
   - 所有延迟时间使用秒为单位
   - 避免混用毫秒和秒

2. 错误处理：
   - 所有操作都应该有适当的错误处理
   - 使用日志记录错误信息
   - 在UI中显示友好的错误提示

3. 状态管理：
   - 使用taskManager管理任务状态
   - 保持状态转换的清晰性
   - 避免状态混乱

4. 性能优化：
   - 使用防抖处理频繁操作
   - 优化DOM操作
   - 合理设置操作间隔

## 许可证

[MIT License](LICENSE)

---

Generated by [create-chrome-ext](https://github.com/guocaoyi/create-chrome-ext)
