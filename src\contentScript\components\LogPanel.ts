import { logger } from '../utils/logger'
import type { LogEntry } from '../utils/logger'
import { effect } from '../utils/reactive'

// 添加防抖函数
function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

export function createLogPanel() {
  const logPanel = document.createElement('div')
  logPanel.className = 'log-panel'
  logPanel.style.zIndex = '999998' // 确保在工具栏之下

  // 创建日志面板内容
  logPanel.innerHTML = `
    <style>
      .log-entry.success {
        color: #67C23A;
      }
      .log-entry.success .log-timestamp {
        color: #67C23A;
      }
    </style>
    <div class="log-panel-header">
      <div class="log-panel-title">操作日志</div>
      <button class="clear-button">清除</button>
    </div>
    <div class="log-panel-content"></div>
  `

  return logPanel
}

export function setupLogPanel(panel: HTMLElement) {
  const logContent = panel.querySelector('.log-panel-content') as HTMLElement
  const clearButton = panel.querySelector('.clear-button') as HTMLElement
  let isAutoScrollEnabled = true

  // 创建固定数量的 DOM 元素池
  const createLogPool = () => {
    logContent.innerHTML = '' // 清空现有内容
    for (let i = 0; i < 30; i++) {
      const logEntry = document.createElement('div')
      logEntry.className = 'log-entry'
      logEntry.style.display = 'none' // 默认隐藏
      logEntry.innerHTML = `
        <span class="log-timestamp"></span>
        <span class="log-message"></span>
      `
      logContent.appendChild(logEntry)
    }
  }

  // 更新日志显示
  function updateLogs(logs: LogEntry[]) {
    const entries = logContent.children
    // 更新每个日志条目
    for (let i = 0; i < entries.length; i++) {
      const entry = entries[i] as HTMLElement
      if (i < logs.length) {
        const log = logs[i]
        const time = new Date(log.timestamp).toLocaleTimeString()
        entry.className = `log-entry ${log.type}`
        entry.style.display = 'block'
        const timestamp = entry.querySelector('.log-timestamp')
        const message = entry.querySelector('.log-message')
        if (timestamp) timestamp.textContent = `[${time}]`
        if (message) message.textContent = log.message
      } else {
        entry.style.display = 'none'
      }
    }

    // 自动滚动到顶部（因为新日志在前面）
    if (isAutoScrollEnabled && logs.length > 0) {
      requestAnimationFrame(() => {
        logContent.scrollTop = 0
      })
    }
  }

  // 创建 DOM 元素池
  createLogPool()

  // 防抖的日志更新函数
  const debouncedUpdate = debounce(() => {
    const state = logger.getState().value
    // 处理日志内容更新
    updateLogs(state.logs)
    // 处理面板可见性
    panel.classList.toggle('visible', state.isVisible)
  }, 16)

  // 使用 effect 监听状态变化
  const stop = effect(() => {
    const state = logger.getState().value
    debouncedUpdate()
  })

  // 清除按钮事件
  const clearHandler = () => {
    logger.clearLogs()
  }
  clearButton.addEventListener('click', clearHandler)

  // 鼠标悬停事件
  const mouseEnterHandler = () => {
    isAutoScrollEnabled = false
  }
  const mouseLeaveHandler = () => {
    isAutoScrollEnabled = true
  }
  logContent.addEventListener('mouseenter', mouseEnterHandler)
  logContent.addEventListener('mouseleave', mouseLeaveHandler)

  // 返回清理函数
  return () => {
    stop() // 停止 effect
    clearButton.removeEventListener('click', clearHandler)
    logContent.removeEventListener('mouseenter', mouseEnterHandler)
    logContent.removeEventListener('mouseleave', mouseLeaveHandler)
  }
}
