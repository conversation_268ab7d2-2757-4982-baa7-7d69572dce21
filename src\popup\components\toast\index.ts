import { App, createApp } from 'vue'
import ToastContainer from './ToastContainer.vue'
import type { ToastApi, ToastOptions } from './types'

let instance: any = null

const createInstance = () => {
  if (instance) return instance

  // 创建容器元素
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 创建 Vue 应用实例
  const app = createApp(ToastContainer)
  instance = app.mount(container)

  return instance
}

// 确保只创建一次实例
const getInstance = (): any => {
  if (!instance) {
    instance = createInstance()
  }
  return instance
}

export const toast: ToastApi = {
  show: (options: ToastOptions) => getInstance().show(options),
  success: (message: string, duration?: number) => getInstance().success(message, duration),
  error: (message: string, duration?: number) => getInstance().error(message, duration),
  info: (message: string, duration?: number) => getInstance().info(message, duration),
  warning: (message: string, duration?: number) => getInstance().warning(message, duration),
  clear: () => getInstance().clear()
}

export const install = (app: App) => {
  // 在插件安装时就创建实例
  getInstance()
  app.config.globalProperties.$toast = toast
}

export default {
  install
}
