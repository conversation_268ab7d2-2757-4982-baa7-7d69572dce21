import { defineManifest } from '@crxjs/vite-plugin'
import packageData from '../package.json'

//@ts-ignore
const isDev = process.env.NODE_ENV == 'development'

export default defineManifest({
  name: `${packageData.displayName || packageData.name}${isDev ? ` ➡️ Dev` : ''}`,
  description: packageData.description,
  version: packageData.version,
  author: {
    email: 'D4K8888',
  },
  manifest_version: 3,
  icons: {
    16: 'img/logo-16.png',
    32: 'img/logo-32.png',
    48: 'img/logo-48.png',
    128: 'img/logo-128.png',
  },
  action: {
    default_popup: 'popup.html',
    default_icon: 'img/logo-48.png',
  },
  background: {
    service_worker: 'src/background/index.ts',
    type: 'module',
  },
  content_scripts: [
    {
      matches: [
        'https://seller-sg.tiktok.com/product/opportunity*',
        'https://seller-ph.tiktok.com/product/opportunity*',
        'https://seller-id.tiktok.com/product/opportunity*',
        "https://seller-id.tokopedia.com/product/opportunity*",
        'https://seller-my.tiktok.com/product/opportunity*',
        'https://seller-th.tiktok.com/product/opportunity*',
        'https://seller-vn.tiktok.com/product/opportunity*',
        'https://seller.tiktokshopglobalselling.com/product/opportunity*',
        'https://seller.tiktokglobalshop.com/product/opportunity*',
        "https://seller-us.tiktok.com/product/opportunity*",
        'https://seller.us.tiktokglobalshop.com/product/opportunity*',
        "https://seller.us.tiktokshopglobalselling.com/product/opportunity",
        'https://seller.eu.tiktokglobalshop.com/product/opportunity*',
        'https://seller.eu.tiktokshopglobalselling.com/product/opportunity*',
        'https://seller-uk.tiktok.com/product/opportunity*',
        'https://seller-jp.tiktok.com/product/opportunity*',
        "https://seller-de.tiktok.com/product/opportunity*",
        'https://*/*',
        'http://*/*',
      ],
      js: ['src/contentScript/core/index.ts'],
    },
  ],
  web_accessible_resources: [
    {
      resources: ['img/logo-16.png', 'img/logo-32.png', 'img/logo-48.png', 'img/logo-128.png'],
      matches: [],
    },
  ],
  permissions: ['storage', 'system.cpu', 'system.display', 'tabs'],
  host_permissions: [],
})
