<template>
  <div id="popup" class="theme-bg flex flex-col w-720px h-600px">
    <!-- 顶部固定区域 -->
    <div class="flex flex-col w-full">
      <!-- 标题 -->
      <h1 class="text-2xl font-bold text-primary-600 text-center py-3 flex items-center justify-center gap-2 relative">
        <i class="i-ri-shopping-cart-2-line text-3xl"></i>
        {{ baseInfo.name }}
      </h1>

      <!-- Tab 切换 -->
      <div class="flex theme-text text-xl font-bold border-b border-gray-200 dark:border-gray-700">
        <button v-for="tab in baseInfo.tabs" :key="tab.id" @click="activeTab = tab.id"
          class="flex-1 py-1.5 px-4 text-center transition-colors flex items-center justify-center gap-1" :class="[
            activeTab === tab.id
              ? 'text-primary border-b-2 border-primary -mb-px'
              : 'text-gray-500 hover:text-primary'
          ]">
          <i v-if="tab.id === 'auth'" class="i-ri-key-line"></i>
          <i v-else-if="tab.id === 'guide'" class="i-ri-book-open-line"></i>
          {{ tab.name }}
        </button>
      </div>
    </div>

    <!-- 中间可滚动区域 -->
    <div class="flex-1 overflow-y-auto overflow-x-hidden">
      <div class="h-full">
        <!-- 授权信息 -->
        <div v-if="activeTab === 'auth'" class="flex items-center justify-center h-full w-full">
          <div v-if="isAuthorized" class="flex flex-col items-center justify-center gap-6 w-full p-6">
            <!-- 授权信息卡片 -->
            <div class="w-full max-w-2xl bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <div class="grid grid-cols-2 gap-6">
                <!-- 授权状态 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400 text-sm mb-1">
                    <i class="i-ri-shield-check-line text-xl"></i>
                    授权状态
                  </div>
                  <div class="text-success text-lg font-semibold">
                    已授权
                  </div>
                </div>
                
                <!-- 过期时间 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400 text-sm mb-1">
                    <i class="i-ri-timer-line text-xl"></i>
                    过期时间
                  </div>
                  <div class="text-primary text-lg font-semibold">
                    {{ expireTime }}
                  </div>
                </div>
                
                <!-- 身份码 -->
                <div class="col-span-2 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div class="flex items-center gap-2 text-gray-600 dark:text-gray-400 text-sm mb-1">
                    <i class="i-ri-fingerprint-line text-xl"></i>
                    身份码
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="flex-1 text-primary text-lg font-semibold truncate">
                      {{ key }}
                    </div>
                    <button @click="copyKey" class="flex items-center justify-center p-2 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors" title="复制身份码">
                      <i class="i-ri-file-copy-line text-xl text-gray-600 dark:text-gray-400"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 打开商铺按钮 -->
            <button @click="openShopPage" class="mt-4 w-full max-w-2xl bg-primary hover:bg-primary-600 text-white rounded-lg py-3 px-6 transition-colors flex items-center justify-center gap-2">
              <i class="i-ri-store-2-line text-xl"></i>
              <span>打开商铺</span>
            </button>
          </div>
          <div v-else class="flex flex-col gap-6 w-full items-center justify-center ">
            <!-- 身份标识和授权码输入区域 -->
            <div class="w-2/3 flex flex-col gap-3 mx-auto">
              <!-- 身份标识区域 -->
              <div class="flex items-center gap-2">
                <div class="flex-1">
                  <div class="flex items-center gap-2">
                    <i class="i-ri-fingerprint-line text-gray-400"></i>
                    <span class="text-gray-600 dark:text-gray-400">
                      身份码
                    </span>
                    <span v-if="!isMachineAuth && !shopCode" class="text-warning-500 text-xs">
                      (请先打开店铺页面获取身份码)
                    </span>
                  </div>
                  <div class="mt-1">
                    <div class="w-full rounded py-2 px-3 bg-gray-50 text-error-600 select-all font-mono">
                      {{ key }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 授权码输入 -->
              <div class="flex items-center gap-2">
                <div class="flex-1">
                  <div class="flex items-center gap-2">
                    <i class="i-ri-key-line text-gray-400"></i>
                    <span class="text-gray-600 dark:text-gray-400">授权码：</span>
                  </div>
                  <div class="mt-1 relative">
                    <input v-model="secret" type="text" placeholder="请输入授权码"
                      class="w-full rounded py-2 px-3 b-gray-200 b-1 outline-none focus:ring-primary focus:ring-1 focus:b-primary" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 流程按钮组 -->
            <div class="w-2/3 grid gap-2 mx-auto"
              :class="{ 'grid-cols-3': isMachineAuth, 'grid-cols-4': !isMachineAuth }">
              <button v-for="button in buttons.filter(b => b.show)" :key="button.icon" @click="button.action" :class="`
                h-10 px-2 rounded transition-colors flex items-center justify-center gap-0.5 text-xs bg-${button.type} text-white hover:bg-${button.type}-600',
               
              `">
                <i :class="button.icon"></i>
                <span>{{ button.text }}</span>
              </button>

            </div>
          </div>
        </div>

        <!-- 使用说明 -->
        <div v-if="activeTab === 'guide'" class="space-y-2">
          <!-- 功能特点卡片 -->
          <div class="rounded-lg bg-gray-50 dark:bg-gray-800 p-3">
            <h3 class="text-base font-semibold text-primary mb-2 flex items-center gap-1.5">
              <i :class="feturesCard.icon"></i>{{ feturesCard.title }}
            </h3>
            <div class="grid grid-cols-3 gap-2">
              <div v-for="(feature, index) in feturesCard.items" :key="index"
                class="flex flex-col p-2 rounded bg-white dark:bg-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div class="flex items-center gap-1 mb-0.5">
                  <div class="text-sm text-primary">{{ feature.icon }}</div>
                  <div class="font-semibold text-xs">{{ feature.title }}</div>
                </div>
                <div class="text-gray-600 dark:text-gray-300 text-xs leading-tight mt-1">{{ feature.description }}</div>
              </div>
            </div>
          </div>

          <!-- 售后支持卡片 -->
          <div class="rounded-lg bg-gray-50 dark:bg-gray-800 p-3">
            <h3 class="text-base font-semibold text-primary mb-2 flex items-center gap-1.5">
              <i :class="supportCard.icon"></i>{{ supportCard.title }}
            </h3>
            <div class="grid grid-cols-2 gap-1.5">
              <div v-for="item in supportCard.items" :key="item.icon"
                class="flex items-center gap-1.5 p-1.5 rounded bg-white dark:bg-gray-700 shadow-sm">
                <i :class="item.icon"></i>
                <div>
                  <div class="font-medium text-xs font-semibold">{{ item.title }}</div>
                  <div class="text-gray-600 dark:text-gray-300 text-xs leading-tight mt-1">{{ item.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部固定区域 -->
    <div
      class="border-t border-gray-200 dark:border-gray-700 p-4 text-center text-sm text-gray-500 dark:text-gray-400 min-h-12 flex items-center justify-center bg-white dark:bg-gray-900"
      @click="openWeixin" title="点击复制微信号并打开微信">
      <p class="flex items-center justify-center gap-2">
        {{ baseInfo.contact.prefix }}
        <i
          class="i-ri-wechat-fill text-green-500 cursor-pointer hover:text-green-600 transition-colors text-xl min-w-5 min-h-5"></i>
        <span class="text-primary cursor-pointer hover:text-primary-600 transition-colors">{{ baseInfo.contact.wechat
          }}</span>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { generateMachineCode, checkAuth, formatExpireTime } from '../utils/auth'
import { saveKey, queryKey, saveSecret, querySecret, saveMachineCode, queryMachineCode, queryShopCode, clearAuthInfo } from '../utils/storage'
import { toast } from './components/toast'

// 常量表及类型定义

type TabType = 'auth' | 'guide'
type AuthType = 'SHOP' | 'MACHINE'
const AUTH_TYPE = import.meta.env.VITE_AUTH_TYPE as AuthType
const SHOP_PAGE_URL = 'https://seller.tiktokglobalshop.com/product/opportunity'

// 响应式变量定义

const activeTab = ref<TabType>('auth')
const isAuthorized = ref(false)
const machineCode = ref('')
const shopCode = ref('')
const secret = ref('')
const expireTime = ref('')
const isMachineAuth = computed(() => AUTH_TYPE === 'MACHINE')
const key = computed(() => {
  if (isMachineAuth.value) {
    return machineCode.value
  }
  if (!machineCode.value) return ''
  if (!shopCode.value) return machineCode.value
  return `${machineCode.value}-${shopCode.value}`
})

// 页面基本结构信息
//@ts-ignore
const wechat = chrome.runtime.getManifest()?.author?.email || 'D4K8888'
const name = chrome.runtime.getManifest()?.name || 'TikTok大咖'
const baseInfo = {
  name: name,
  tabs: [
    { id: 'auth' as TabType, name: '授权信息' },
    { id: 'guide' as TabType, name: '使用说明' }
  ],
  contact: {
    prefix: '同步TikTok页面玩法、售后、更新、安装指导、TK拿流量玩法、点击添加 ',
    wechat: wechat
  }
}

const feturesCard =
{
  icon: "i-ri-function-line",
  title: "功能特点",
  items: [
    {
      icon: '✨',
      title: '热门搜索词提报',
      description: 'TikTok商城免费搜索流量'
    },
    {
      icon: '✨',
      title: '热门话题标签提报',
      description: 'TikTok带货视频标签免费曝光'
    },
    {
      icon: '✨',
      title: '热门商品关联',
      description: '猜你喜欢免费流量'
    },
    {
      icon: '✨',
      title: '移除重复关键词',
      description: '避免违规扣分'
    },
    {
      icon: '✨',
      title: '一键添加热门关键词',
      description: '有效增加商品浏览次数'
    },
    {
      icon: '✨',
      title: '搜索热门标题提报',
      description: '精准搜索热门'
    },
    {
      icon: '🔄',
      title: '开发中：多平台选品插件',
      description: '截流玩法'
    }
  ]
}
const supportCard =
{
  icon: "i-ri-window-line",
  title: "浏览器支持",
  items: [
    {
      icon: "i-ri-window-line",
      title: "浏览器支持",
      description: "所有浏览器"
    },
    {
      icon: "i-ri-store-line",
      title: "店铺支持",
      description: "本土和跨境(美区、英区、欧盟、东南亚)"
    },
    {
      icon: "i-ri-service-line",
      title: "售后服务",
      description: "售后、更新、安装指导"
    },
    {
      icon: "i-ri-wechat-line",
      title: "联系方式",
      description: `微信：${wechat}`
    }

  ]
}

const buttons =
  [{
    icon: 'i-ri-store-2-line',
    text: "打开商城",
    action: () => {
      openShopPage()
    },
    show: AUTH_TYPE === "SHOP",
    type: "info"
  },
  {
    icon: 'i-ri-file-copy-line',
    text: "复制身份码",
    action: () => {
      copyKey()
    },
    show: true,
    type: "warning"
  },
  {
    icon: 'i-ri-wechat-line',
    text: "添加微信",
    action: () => {
      openWeixin()
    },
    show: true,
    type: "error"
  },
  {
    icon: 'i-ri-key-line',
    text: "授权激活",
    action: () => {
      verifyAuth()
    },
    show: true,
    type: "success"
  }
  ]



// 初始化授权信息
const initAuthInfo = async () => {
  try {
    // 获取或生成机器码
    let code = await queryMachineCode()
    if (!code) {
      code = await generateMachineCode()
      await saveMachineCode(code)
    }
    machineCode.value = code

    // 如果是店铺验证模式，获取店铺码
    if (!isMachineAuth.value) {
      const shop = await queryShopCode()
      if (shop) {
        shopCode.value = shop
      }
    }
    // 查询保存 可key 和 secret
    const savedKey = await queryKey()
    const savedSecret = await querySecret()
    if (savedKey && savedSecret) {
      const result = await checkAuth(savedSecret, {
        currentMachineCode: machineCode.value,
        currentShopCode: !isMachineAuth.value ? shopCode.value : undefined
      })
      if (result.isValid && result.expireTime) {
        isAuthorized.value = true
        expireTime.value = formatExpireTime(result.expireTime)
      } else {
        await clearAuthInfo()
      }
    }
  } catch (error) {
    toast.error('初始化授权信息失败')
  }
}

const copyKey = async () => {
  if (!isMachineAuth.value && !shopCode.value) {
    toast.error('请先打开店铺页面获取店铺标识')
    return
  }

  try {
    await navigator.clipboard.writeText(key.value)
    toast.success('身份标识已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败，请手动复制')
  }
}

const verifyAuth = async () => {
  if (!isMachineAuth.value && !shopCode.value) {
    toast.error('请先打开店铺页面获取店铺标识')
    return
  }

  if (!secret.value) {
    toast.error('请输入授权码')
    return
  }

  try {
    const result = await checkAuth(secret.value, {
      currentMachineCode: machineCode.value,
      currentShopCode: !isMachineAuth.value ? shopCode.value : undefined
    })
    
    if (result.isValid && result.expireTime) {
      await saveKey(key.value)
      await saveSecret(secret.value)
      isAuthorized.value = true
      expireTime.value = formatExpireTime(result.expireTime)
      toast.success('授权验证成功！')
    } else {
      toast.error('授权码无效或已过期')
    }
  } catch (error) {
    toast.error('授权验证失败，请检查授权码是否正确')
  }
}

const openWeixin = async () => {
  try {
    window.location.href = 'weixin://'
    toast.info('正在打开微信...')
    try {
      await navigator.clipboard.writeText(baseInfo.contact.wechat)
      toast.success('微信号已复制到剪贴板')
    } catch (error) {
      toast.error('复制失败，请手动复制')
    }
  } catch (error) {
    toast.error('无法打开微信，请手动打开')
  }
}

// 打开店铺页面
const openShopPage = async () => {
  try {
    // 检查是否已经有打开的店铺页面
    const tabs = await chrome.tabs.query({ url: SHOP_PAGE_URL })

    if (tabs.length > 0) {
      // 如果已经有打开的页面，切换到该页面
      await chrome.tabs.update(tabs[0].id!, { active: true })
      await chrome.windows.update(tabs[0].windowId, { focused: true })
    } else {
      // 否则打开新标签页
      await chrome.tabs.create({ url: SHOP_PAGE_URL })
    }
  } catch (error) {
    
    toast.error('打开店铺页面失败')
  }
}

onMounted(async () => {
  await initAuthInfo()
  chrome.runtime.onMessage.addListener((message) => {
    if (message.type === 'SHOP_CODE_UPDATED') {
      shopCode.value = message.shopCode
    }
  })

})
// 监听来自 content script 的消息


</script>

<style scoped></style>
