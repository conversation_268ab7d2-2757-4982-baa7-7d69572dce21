import { ref, Ref } from './reactive'

export interface LogEntry {
  type: LogLevel
  message: string
  timestamp: number
}

export interface LogState {
  logs: LogEntry[]
  isVisible: boolean
}

type LogLevel = 'info' | 'error' | 'warning' | 'debug' | 'success'

class Logger {
  private static instance: Logger
  private maxLogs = 30 // 最大日志数量限制为30条
  private state: Ref<LogState>

  private constructor() {
    this.state = ref<LogState>({
      logs: [],
      isVisible: false
    })
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  // 获取状态
  getState(): Ref<LogState> {
    return this.state
  }

  // 设置日志面板可见性
  setVisibility(isVisible: boolean) {
    this.state.value = {
      ...this.state.value,
      isVisible
    }
  }

  // 添加日志
  private addLog(type: LogLevel, message: string) {
    const entry: LogEntry = {
      type,
      message,
      timestamp: Date.now()
    }

    // 保持最新的3条日志
    this.state.value = {
      ...this.state.value,
      logs: [entry, ...this.state.value.logs].slice(0, this.maxLogs)
    }
  }

  // 清除所有日志
  clearLogs() {
    this.state.value = {
      ...this.state.value,
      logs: []
    }
  }

  // 公共日志方法
  info(message: string) {
    this.addLog('info', message)
  }

  error(message: string) {
    this.addLog('error', message)
  }

  warning(message: string) {
    this.addLog('warning', message)
  }

  debug(message: string) {
    this.addLog('debug', message)
  }

  success(message: string) {
    this.addLog('success', message)
  }
}

// 导出单例实例
export const logger = Logger.getInstance()
