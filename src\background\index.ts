/**
 * 后台脚本
 */

import CryptoJS from 'crypto-js'

// 应用密钥 - 从环境变量获取
const APP_SECRET = import.meta.env.VITE_APP_SECRET || 'your-secret-key-2024'
const AUTH_TYPE = import.meta.env.VITE_AUTH_TYPE || 'SHOP'

// 授权信息接口
interface AuthInfo {
  identifier: string
  expireTime: Date
}

interface VerifyAuthInfo extends AuthInfo {
  isValid: boolean
  error?: string
}

// CPU 信息接口
interface HardwareInfo {
  cpuModel: string
  cpuArch: string
  cpuFeatures: string[]
  cpuCount: number
  displayId: string
  displayName: string
  displayResolution: string
  hasTouchSupport: boolean
}

// 生成机器码
async function generateMachineCode(): Promise<string> {
  try {
    // 收集系统信息
    const [cpuInfo, displayInfo] = await Promise.all([
      chrome.system.cpu.getInfo(),
      chrome.system.display.getInfo(),
    ])

    // 获取主显示器信息
    const primaryDisplay = displayInfo.find((d) => d.isPrimary) || displayInfo[0]

    // 构建硬件信息对象
    const hardwareInfo: HardwareInfo = {
      // CPU 信息
      cpuModel: cpuInfo.modelName,
      cpuArch: cpuInfo.archName,
      cpuFeatures: cpuInfo.features || [],
      cpuCount: cpuInfo.numOfProcessors,

      // 显示器信息
      displayId: primaryDisplay?.id?.toString() || '',
      displayName: primaryDisplay?.name || '',
      displayResolution: primaryDisplay
        ? `${primaryDisplay.bounds.width}x${primaryDisplay.bounds.height}`
        : '',
      hasTouchSupport: primaryDisplay?.hasTouchSupport || false,
    }

    // 生成较短的机器码
    const hash = CryptoJS.MD5(JSON.stringify(hardwareInfo)).toString()
    return hash.match(/.{4}/g)?.join('-') || hash
  } catch (error) {

    return ''
  }
}

// 验证授权码
function checkAuth(
  authSecret: string,
  options?: {
    currentShopCode?: string
    currentMachineCode?: string
  },
): VerifyAuthInfo {
  try {
    // 输入参数验证
    if (!authSecret?.trim()) {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '授权密钥不能为空'
      }
    }

    // 根据授权类型验证必要参数
    if (AUTH_TYPE === 'SHOP' && !options?.currentShopCode?.trim()) {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '店铺码不能为空'
      }
    }
    if (AUTH_TYPE === 'MACHINE' && !options?.currentMachineCode?.trim()) {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '机器码不能为空'
      }
    }

    const bytes = CryptoJS.AES.decrypt(authSecret, APP_SECRET)
    const decrypted = bytes.toString(CryptoJS.enc.Utf8)


    // 验证解密后的数据格式
    if (!decrypted || typeof decrypted !== 'string') {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '无效的授权密钥'
      }
    }

    const parts = decrypted.split('|')

    if (parts.length !== 2) {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '授权密钥格式错误'
      }
    }

    const [identifier, expireTimeStr] = parts

    // 验证标识符
    if (!identifier?.trim()) {
      return {
        isValid: false,
        identifier: '',
        expireTime: new Date(),
        error: '标识符不能为空'
      }
    }

    // 验证时间戳
    const expireTimeNum = parseInt(expireTimeStr)
    if (isNaN(expireTimeNum)) {
      return {
        isValid: false,
        identifier,
        expireTime: new Date(),
        error: '无效的过期时间'
      }
    }

    const expireTime = new Date(expireTimeNum)
    if (expireTime.toString() === 'Invalid Date') {
      return {
        isValid: false,
        identifier,
        expireTime: new Date(),
        error: '无效的过期时间格式'
      }
    }

    // 检查是否已过期
    if (expireTime <= new Date()) {
      return {
        isValid: false,
        identifier,
        expireTime,
        error: '授权已过期'
      }
    }

    // 根据授权类型解析和验证身份码
    if (AUTH_TYPE === 'SHOP') {
      // 店铺授权模式：验证完整身份标识符
      const expectedIdentifier = `${options?.currentMachineCode}-${options?.currentShopCode}`



      if (identifier !== expectedIdentifier) {
        return {
          isValid: false,
          identifier,
          expireTime,
          error: '身份标识符不匹配'
        }
      }
    } else if (AUTH_TYPE === 'MACHINE') {
      // 机器授权模式：验证机器码格式和匹配
      if (!identifier || !/^[A-Za-z0-9-]{1,64}$/.test(identifier)) {
        return {
          isValid: false,
          identifier,
          expireTime,
          error: '无效的机器码格式'
        }
      }

      if (identifier !== options?.currentMachineCode) {
        return {
          isValid: false,
          identifier,
          expireTime,
          error: '机器码不匹配'
        }
      }
    }

    // 验证通过
    return {
      isValid: true,
      identifier,
      expireTime,
    }
  } catch (error) {
    return {
      isValid: false,
      identifier: '',
      expireTime: new Date(),
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

// 监听来自 content script 和 popup 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'GENERATE_MACHINE_CODE') {
    generateMachineCode()
      .then(code => {
        sendResponse({ success: true, machineCode: code })
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message })
      })
    return true // 表示会异步发送响应
  }

  if (request.type === 'CHECK_AUTH') {
    const { authSecret, options } = request
    const result = checkAuth(authSecret, options)
    sendResponse(result)
  }
})








