import { getWechatNumber } from '../utils/utils'
import { checkAuth, formatExpireTime, generateMachineCode } from '../../utils/auth'
import { querySecret, saveSecret, queryMachineCode, saveMachineCode, queryShopCode, saveShopCode } from '../../utils/storage'
import { checkShopInfoOpened, openShopInfo, closeShopInfo, getShopCode } from '../dom/actions'
import { toast } from './toast'

const AUTH_TYPE = import.meta.env.VITE_AUTH_TYPE || 'SHOP'
const isMachineAuth = AUTH_TYPE === 'MACHINE'

interface AuthPanelOptions {
  onConfirm?: (value: string) => void | Promise<void>
}

const createAuthPanel = async (options: AuthPanelOptions) => {
  // 创建遮罩层
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999999;
  `

  // 创建面板容器
  const panel = document.createElement('div')
  panel.style.cssText = `
    background: #ffffff;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-width: 420px;
    width: 90%;
    position: relative;
  `

  // 添加关闭按钮
  const closeButton = document.createElement('button')
  closeButton.style.cssText = `
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: #f1f5f9;
    color: #64748b;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    padding: 0;
    line-height: 1;
  `
  closeButton.innerHTML = '×'
  closeButton.onclick = () => {
    document.body.removeChild(overlay)
  }
  closeButton.onmouseover = () => {
    closeButton.style.background = '#e2e8f0'
    closeButton.style.color = '#475569'
  }
  closeButton.onmouseout = () => {
    closeButton.style.background = '#f1f5f9'
    closeButton.style.color = '#64748b'
  }

  // 标题
  const title = document.createElement('div')
  title.style.cssText = `
    font-size: 18px;
    font-weight: 600;
    color: #1d4ed8;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
  `
  title.innerHTML = '<i class="i-ri-shield-keyhole-line"></i>授权验证'

  // 身份码区域
  const identitySection = document.createElement('div')
  identitySection.style.cssText = `
    margin-bottom: 20px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  `

  // 身份码标题
  const identityTitle = document.createElement('div')
  identityTitle.style.cssText = `
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 12px;
    color: #475569;
    font-size: 14px;
  `
  identityTitle.innerHTML = '<i class="i-ri-fingerprint-line"></i>身份码'

  // 身份码值容器
  const identityValue = document.createElement('div')
  identityValue.style.cssText = `
    font-family: monospace;
    font-size: 14px;
    color: #1e40af;
    word-break: break-all;
    display: flex;
    align-items: center;
    gap: 8px;
  `

  // 获取身份码
  let machineCode = await queryMachineCode()
  if (!machineCode) {
    machineCode = await generateMachineCode()
    if (machineCode) {
      await saveMachineCode(machineCode)
    } else {
      toast.error('无法生成机器码')
      return overlay
    }
  }

  let identityCode = ''
  let shopCode: string = ''

  if (isMachineAuth) {
    identityCode = machineCode
  } else {
    // 商铺模式
    const savedShopCode = await queryShopCode()
    if (savedShopCode) {
      shopCode = savedShopCode
    } else {
      // 检查店铺信息是否已打开
      const isShopInfoOpened = checkShopInfoOpened()
      if (!isShopInfoOpened) {
        // 如果未打开，则打开店铺信息
        await openShopInfo()
      }

      try {
        // 获取店铺码
        shopCode = getShopCode()
        // 更新店铺码
        await saveShopCode(shopCode)

        // 如果之前是关闭状态，则关闭店铺信息
        if (!isShopInfoOpened) {
          await closeShopInfo()
        }
      } catch (error) {

        // shopCode 保持为空字符串
      }
    }

    identityCode = shopCode ? `${machineCode}-${shopCode}` : machineCode
  }

  // 设置身份码显示
  const codeDisplay = document.createElement('span')
  codeDisplay.style.cssText = `flex: 1;`
  codeDisplay.textContent = identityCode || '正在获取...'

  // 复制按钮
  const copyButton = document.createElement('button')
  copyButton.style.cssText = `
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    background: #e2e8f0;
    color: #475569;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  `
  copyButton.textContent = '复制'
  copyButton.onclick = async () => {
    try {
      await navigator.clipboard.writeText(identityCode)
      toast.success('身份码已复制')
    } catch (error) {
      toast.error('复制失败，请手动复制')
    }
  }

  identityValue.appendChild(codeDisplay)
  identityValue.appendChild(copyButton)

  // 授权码输入区域
  const inputSection = document.createElement('div')
  inputSection.style.cssText = `
    margin-bottom: 20px;
  `

  const inputTitle = document.createElement('div')
  inputTitle.style.cssText = `
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
    color: #475569;
    font-size: 14px;
  `
  inputTitle.innerHTML = '<i class="i-ri-key-line"></i>授权码'

  const input = document.createElement('input')
  input.style.cssText = `
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    outline: none;
    font-size: 14px;
    background: #f8fafc;
    color: #1e293b;
    transition: all 0.2s;
  `
  input.placeholder = '请输入授权码'

  // 按钮容器
  const buttonContainer = document.createElement('div')
  buttonContainer.style.cssText = `
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 24px;
  `

  // 添加微信按钮
  const wechatButton = document.createElement('button')
  wechatButton.style.cssText = `
    padding: 8px;
    border: none;
    border-radius: 6px;
    background: #10b981;
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    hover:bg-green-600;
  `
  wechatButton.innerHTML = '<i class="i-ri-wechat-line"></i>添加微信'
  wechatButton.onclick = async () => {
    const wechatNumber = await getWechatNumber()
    try {
      await navigator.clipboard.writeText(wechatNumber)
      toast.success('微信号已复制')
      window.location.href = 'weixin://'
    } catch (error) {
      toast.error('复制失败，请手动复制')
    }
  }

  // 验证按钮
  const verifyButton = document.createElement('button')
  verifyButton.style.cssText = `
    padding: 8px;
    border: none;
    border-radius: 6px;
    background: #ef4444;
    color: white;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    hover:bg-red-600;
  `
  verifyButton.innerHTML = '<i class="i-ri-shield-check-line"></i>验证授权'
  verifyButton.onclick = async () => {
    if (!input.value) {
      toast.error('请输入授权码')
      return
    }

    // 确保有机器码
    if (!machineCode) {
      machineCode = await generateMachineCode()
      if (machineCode) {
        await saveMachineCode(machineCode)
      } else {
        toast.error('无法生成机器码')
        return
      }
    }

    if (!isMachineAuth && !shopCode) {
      toast.error('请先获取店铺标识')
      return
    }

    try {
      const result = await checkAuth(input.value, {
        currentMachineCode: machineCode,
        currentShopCode: !isMachineAuth ? shopCode : undefined
      })

      if (result.isValid && result.expireTime) {
        await saveSecret(input.value)
        toast.success(`授权成功，有效期至：${formatExpireTime(result.expireTime)}`)
        if (options.onConfirm) {
          await options.onConfirm(input.value)
        }
        document.body.removeChild(overlay)
      } else {
        toast.error(result.error || '授权码无效')
      }
    } catch (error) {
      toast.error('验证失败，请检查授权码是否正确')
    }
  }

  buttonContainer.appendChild(wechatButton)
  buttonContainer.appendChild(verifyButton)

  // 组装面板
  identitySection.appendChild(identityTitle)
  identitySection.appendChild(identityValue)

  inputSection.appendChild(inputTitle)
  inputSection.appendChild(input)

  panel.appendChild(title)
  panel.appendChild(closeButton)
  panel.appendChild(identitySection)
  panel.appendChild(inputSection)
  panel.appendChild(buttonContainer)

  overlay.appendChild(panel)

  return overlay
}

export const authPanel = {
  show: async (options: AuthPanelOptions = {}): Promise<void> => {
    return new Promise((resolve) => {
      createAuthPanel({
        ...options,
        onConfirm: async (value) => {
          if (options.onConfirm) {
            await options.onConfirm(value)
          }
          resolve()
        }
      }).then(element => {
        document.body.appendChild(element)
        const closeButton = element.querySelector('button')
        if (closeButton) {
          const originalOnClick = closeButton.onclick
          closeButton.onclick = (event) => {
            if (originalOnClick) {
              originalOnClick.call(closeButton, event)
            }
            resolve()
          }
        }
      })
    })
  }
}
