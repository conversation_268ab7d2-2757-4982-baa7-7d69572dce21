<template>
  <Transition
    enter-active-class="transition duration-300 ease-out"
    enter-from-class="transform translate-y-2 opacity-0"
    enter-to-class="transform translate-y-0 opacity-100"
    leave-active-class="transition duration-200 ease-in"
    leave-from-class="transform translate-y-0 opacity-100"
    leave-to-class="transform translate-y-2 opacity-0"
  >
    <div
      class="px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 min-w-64 max-w-80"
      :class="[
        type === 'success' ? 'bg-success text-white' :
        type === 'error' ? 'bg-error text-white' :
        type === 'warning' ? 'bg-warning text-white' :
        'bg-info text-white'
      ]"
    >
      <i :class="[
        type === 'success' ? 'i-ri-checkbox-circle-line' :
        type === 'error' ? 'i-ri-error-warning-line' :
        type === 'warning' ? 'i-ri-alert-line' :
        'i-ri-information-line'
      ]"></i>
      <span class="flex-1">{{ message }}</span>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ToastType } from './types'

defineProps<{
  message: string,
  type: ToastType,
}>()
</script> 
