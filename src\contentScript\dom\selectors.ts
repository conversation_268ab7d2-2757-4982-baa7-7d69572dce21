// 选择器配置的类型定义
interface Selector {
  /** 选择器字符串 */
  selector: string
  /** 选择器描述 */
  description: string
}

interface PageSelectors {
  [key: string]: Selector
}

interface SelectorsConfig {
  [urlPattern: string]: PageSelectors
}

// 选择器配置
export const selectors: SelectorsConfig = {
  'seller-ph\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
    productName: {
      selector: '.content.w-full.text-neutral-text1.text-body-m-medium',
      description: '商品名称',
    },
  },
  'seller-id\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
    productName: {
      selector: '.content.w-full.text-neutral-text1.text-body-m-medium',
      description: '商品名称',
    },
  },
  'seller-id\\.tokopedia\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-sg\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-my\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-th\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-vn\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller\\.tiktokshopglobalselling\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
  },
  'seller\\.tiktokglobalshop\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  // !欧盟
  'seller\\.eu\\.tiktokglobalshop\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '下一步按钮 - 通过文本内容识别',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '提交按钮 - 通过文本内容识别',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer .core-space .core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"].core-pagination',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-tabs-content-item .core-grid-item.pulse-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
       '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller\\.eu\\.tiktokshopglobalselling\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:'[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-uk\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector: '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  // !美区
  'seller\\.us\\.tiktokglobalshop\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '下一步按钮 - 通过文本内容识别',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '提交按钮 - 通过文本内容识别',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer .core-space .core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"].core-pagination',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-tabs-content-item .core-grid-item.pulse-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
       '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller\\.us\\.tiktokshopglobalselling\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '下一步按钮 - 通过文本内容识别',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer .core-space .core-btn-primary',
      description: '提交按钮 - 通过文本内容识别',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer .core-space .core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"].core-pagination',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-tabs-content-item .core-grid-item.pulse-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
       '[data-tid="m4b_tabs_pane"] .core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  'seller-us\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
        '.core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-secondary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  //! 日区
  'seller-jp\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
        '.core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
  // ! 德国
   'seller-de\\.tiktok\\.com\\/product\\/opportunity.*': {
    openShopInfo: {
      selector: '[data-tid="workbench.topbar.user"] div',
      description: '点击店铺信息',
    },
    getShopCode: {
      selector: '.theme-arco-popover-inner-content',
      description: '获取店铺码',
    },
    checkShopInfoOpened: {
      selector: '.theme-arco-popover-open',
      description: '店铺信息已打开',
    },
    checkInitAvailable: {
      selector: '#GEC-main',
      description: '检查是否加载完成',
    },
    checkGridLayout: {
      selector: '.core-grid.pulse-grid',
      description: '检测是否为栅格布局',
    },
    getSelectButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-secondary',
      description: '选择商品按钮',
    },
    checkGoodsItemContainer: {
      selector: '.core-table-td [data-tid="m4b_checkbox"]',
      description: '商品项容器',
    },
    checkGoodsItem: {
      selector: '.core-table-td [data-tid="m4b_checkbox"] input[type="checkbox"]',
      description: '商品项复选框',
    },
    checkGoodsItemChecked: {
      selector: '.core-table-row-checked',
      description: '检查商品是否被选中',
    },
    useKeywords: {
      selector: '.core-drawer-footer [data-tid="m4b_switch"] .core-switch',
      description: '使用关键词',
    },
    nextStepButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '下一步按钮',
    },
    nextStepButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '下一步按钮禁用状态',
    },
    onePageSubmitButton: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary',
      description: '提交按钮',
    },
    onePageSubmitButtonDisabled: {
      selector: '.core-drawer-footer [data-tid="m4b_button"].core-btn-primary[disabled]',
      description: '提交按钮禁用状态',
    },
    closeSidePanelButton: {
      selector: '.core-icon-hover.core-drawer-close-icon',
      description: '关闭侧边栏按钮',
    },
    getOneGoodsPagination: {
      selector: '.core-spin-children [data-tid="m4b_pagination"]',
      description: '获取侧页面分页器',
    },
    paginationCurrentPage: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item-active',
      description: '当前页码',
    },
    paginationPageButton: {
      selector: '.core-spin-children [data-tid="m4b_pagination"] .core-pagination-item',
      description: '页码按钮',
    },
    customInjectButton: {
      selector: '.custom-inject-btn',
      description: '自定义按钮',
    },
    getCollectedGoods: {
      selector: '.core-grid-item .pulse-button-toggle-checked',
      description: '获取商品卡片中已收藏的按钮',
    },
    getCollectedGoodsCard: {
      selector: '.core-grid-item:has(.pulse-button-toggle-checked)',
      description: '获取已收藏商品的卡片容器',
    },
    gridItemContainer: {
      selector: '.core-grid-item',
      description: '栅格布局商品容器',
    },
    drawerWrapper: {
      selector: '[data-tid="m4b_drawer"].core-drawer-wrapper',
      description: '侧边栏容器',
    },
    getReportButtonGrid: {
      selector:
        '.core-grid-item.pulse-grid-item [data-tid="m4b_button"].core-btn.core-btn-primary',
      description: '获取提报按钮',
    },
    checkLoading: {
      selector: '.core-spin-loading-layer-inner',
      description: '检查是否加载完成',
    },
    mainPagePagination: {
      selector: '[data-tid="m4b_tabs_pane"] [data-tid="m4b_pagination"]',
      description: '主页面分页器',
    },
  },
}

/**
 * 根据 URL 和功能名获取选择器
 * @param functionName 功能名称
 * @returns 选择器字符串
 */
export const getSelector: (functionName: string) => string = (functionName) => {
  const currentUrl = window.location.href

  for (const pattern in selectors) {
    const regex = new RegExp(pattern)
    const isMatch = regex.test(currentUrl)

    if (isMatch) {
      const pageSelectors = selectors[pattern]
      const selectorConfig = pageSelectors[functionName as keyof typeof pageSelectors]

      if (selectorConfig) {
        return selectorConfig.selector
      }
    }
  }
  throw new Error(`当前页面未配置选择器 ${functionName}，URL: ${currentUrl}`)
}

// 导出所有选择器
export const _selectors = {
  getShopCode: getSelector('getShopCode'),
  checkShopInfoOpened: getSelector('checkShopInfoOpened'),
  openShopInfo: getSelector('openShopInfo'),
  checkGridLayout: getSelector('checkGridLayout'),
  getSelectButton: getSelector('getSelectButton'),
  checkGoodsItemContainer: getSelector('checkGoodsItemContainer'),
  checkGoodsItem: getSelector('checkGoodsItem'),
  checkGoodsItemChecked: getSelector('checkGoodsItemChecked'),
  useKeywords: getSelector('useKeywords'),
  nextStepButton: getSelector('nextStepButton'),
  onePageSubmitButton: getSelector('onePageSubmitButton'),
  getOneGoodsPagination: getSelector('getOneGoodsPagination'),
  paginationCurrentPage: getSelector('paginationCurrentPage'),
  paginationPageButton: getSelector('paginationPageButton'),
  customInjectButton: getSelector('customInjectButton'),
  checkInitAvailable: getSelector('checkInitAvailable'),
  nextStepButtonDisabled: getSelector('nextStepButtonDisabled'),
  onePageSubmitButtonDisabled: getSelector('onePageSubmitButtonDisabled'),
  closeSidePanelButton: getSelector('closeSidePanelButton'),
  getCollectedGoods: getSelector('getCollectedGoods'),
  getCollectedGoodsCard: getSelector('getCollectedGoodsCard'),
  gridItemContainer: getSelector('gridItemContainer'),
  drawerWrapper: getSelector('drawerWrapper'),
  getReportButtonGrid: getSelector('getReportButtonGrid'),
  checkLoading: getSelector('checkLoading'),
  mainPagePagination: getSelector('mainPagePagination'),
}
