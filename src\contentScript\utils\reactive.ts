// 依赖收集的核心数据结构
const targetMap = new WeakMap<any, Map<any, Set<ReactiveEffect>>>()

// 当前激活的副作用函数
let activeEffect: ReactiveEffect | undefined

// 副作用函数接口
interface ReactiveEffect<T = any> {
  (): T
  _isEffect: boolean
  id: number
  active: boolean
  raw: () => T
  deps: Set<ReactiveEffect<T>>[]
  options: ReactiveEffectOptions
  stop?: () => void
}

// 副作用函数选项
interface ReactiveEffectOptions {
  lazy?: boolean
  scheduler?: (job: ReactiveEffect) => void
  onTrack?: (event: DebuggerEvent) => void
  onTrigger?: (event: DebuggerEvent) => void
  onStop?: () => void
  computed?: boolean
}

// 调试事件
interface DebuggerEvent {
  effect: ReactiveEffect
  target: object
  type: 'track' | 'trigger'
  key: string | symbol | undefined
}

// 操作类型
type TriggerOpTypes = 'set' | 'add' | 'delete' | 'clear'
type TrackOpTypes = 'get' | 'has' | 'iterate'

let uid = 0
const effectStack: ReactiveEffect[] = []

// 响应式标记
export const enum ReactiveFlags {
  IS_REACTIVE = '__v_isReactive',
  RAW = '__v_raw'
}

// ref 标记
const RefSymbol = Symbol('ref')

// ref 接口
export interface Ref<T = any> {
  value: T
  [RefSymbol]: true
}

// 判断是否是 ref
export function isRef<T>(value: any): value is Ref<T> {
  return !!(value && value[RefSymbol])
}

// 创建 ref
export function ref<T>(value: T): Ref<T> {
  if (isRef(value)) {
    return value as Ref<T>
  }
  return createRef(value)
}

// 创建只读 ref
export function shallowRef<T>(value: T): Ref<T> {
  return createRef(value, true)
}

// 解包 ref
export function unref<T>(ref: T | Ref<T>): T {
  return isRef(ref) ? ref.value : ref
}

// 创建 ref 实现
function createRef<T>(rawValue: T, shallow = false): Ref<T> {
  return new RefImpl(rawValue, shallow)
}

class RefImpl<T> {
  private _value: T
  private _rawValue: T
  public readonly [RefSymbol]: true = true

  constructor(value: T, public readonly _shallow = false) {
    this._rawValue = value
    this._value = _shallow ? value : convert(value)
  }

  get value() {
    track(this, 'get', 'value')
    return this._value
  }

  set value(newVal) {
    if (hasChanged(newVal, this._rawValue)) {
      this._rawValue = newVal
      this._value = this._shallow ? newVal : convert(newVal)
      trigger(this, 'set', 'value', this._value)
    }
  }
}

// 工具函数：转换值
function convert<T>(value: T): T {
  return isObject(value) ? reactive(value) : value
}

// 工具函数：判断值是否改变
function hasChanged(value: any, oldValue: any): boolean {
  return !Object.is(value, oldValue)
}

// 自定义 ref
export type CustomRefFactory<T> = (
  track: () => void,
  trigger: () => void
) => {
  get: () => T
  set: (value: T) => void
}

// 创建自定义 ref
export function customRef<T>(factory: CustomRefFactory<T>): Ref<T> {
  return new CustomRefImpl(factory)
}

class CustomRefImpl<T> {
  private _get: () => T
  private _set: (value: T) => void
  public readonly [RefSymbol]: true = true

  constructor(factory: CustomRefFactory<T>) {
    const { get, set } = factory(
      () => track(this, 'get', 'value'),
      () => trigger(this, 'set', 'value')
    )
    this._get = get
    this._set = set
  }

  get value() {
    return this._get()
  }

  set value(newVal) {
    this._set(newVal)
  }
}

// 将对象的所有属性都转换为 ref
export type ToRefs<T = any> = {
  [K in keyof T]: T[K] extends Ref ? T[K] : Ref<T[K]>
}

export function toRefs<T extends object>(object: T): ToRefs<T> {
  const ret: any = {}
  for (const key in object) {
    ret[key] = toRef(object, key)
  }
  return ret
}

// 将对象的某个属性转换为 ref
export function toRef<T extends object, K extends keyof T>(
  object: T,
  key: K
): Ref<T[K]> {
  const val = object[key]
  return isRef(val)
    ? val
    : (new ObjectRefImpl(object, key) as any)
}

class ObjectRefImpl<T extends object, K extends keyof T> {
  public readonly [RefSymbol]: true = true

  constructor(private readonly _object: T, private readonly _key: K) {}

  get value() {
    return this._object[this._key]
  }

  set value(newVal) {
    this._object[this._key] = newVal
  }
}

// 创建响应式效果
export function effect<T = any>(
  fn: () => T,
  options: ReactiveEffectOptions = {}
): ReactiveEffect<T> {
  if ((fn as ReactiveEffect)._isEffect) {
    fn = (fn as ReactiveEffect).raw
  }

  const effect = createReactiveEffect(fn, options)

  if (!options.lazy) {
    effect()
  }

  return effect
}

// 创建响应式效果
function createReactiveEffect<T = any>(
  fn: () => T,
  options: ReactiveEffectOptions
): ReactiveEffect<T> {
  const effect = function reactiveEffect(): unknown {
    if (!effect.active) {
      return options.scheduler ? undefined : fn()
    }

    if (!effectStack.includes(effect)) {
      cleanup(effect)
      try {
        effectStack.push(effect)
        activeEffect = effect
        return fn()
      } finally {
        effectStack.pop()
        activeEffect = effectStack[effectStack.length - 1]
      }
    }
  } as ReactiveEffect

  effect._isEffect = true
  effect.id = uid++
  effect.active = true
  effect.raw = fn
  effect.deps = []
  effect.options = options

  return effect
}

// 清理效果依赖
function cleanup(effect: ReactiveEffect) {
  const { deps } = effect
  if (deps.length) {
    for (let i = 0; i < deps.length; i++) {
      deps[i].delete(effect)
    }
    deps.length = 0
  }
}

// 追踪依赖
export function track(target: object, type: TrackOpTypes, key: unknown) {
  if (!activeEffect) {
    return
  }

  let depsMap = targetMap.get(target)
  if (!depsMap) {
    targetMap.set(target, (depsMap = new Map()))
  }

  let dep = depsMap.get(key)
  if (!dep) {
    depsMap.set(key, (dep = new Set()))
  }

  if (!dep.has(activeEffect)) {
    dep.add(activeEffect)
    activeEffect.deps.push(dep)
  }
}

// 触发更新
export function trigger(
  target: object,
  type: TriggerOpTypes,
  key?: unknown,
  newValue?: unknown,
  oldValue?: unknown
) {
  const depsMap = targetMap.get(target)
  if (!depsMap) {
    return
  }

  const effects = new Set<ReactiveEffect>()
  const computedRunners = new Set<ReactiveEffect>()

  const add = (effectsToAdd: Set<ReactiveEffect> | undefined) => {
    if (effectsToAdd) {
      effectsToAdd.forEach(effect => {
        if (effect !== activeEffect) {
          if (effect.options.computed) {
            computedRunners.add(effect)
          } else {
            effects.add(effect)
          }
        }
      })
    }
  }

  if (key !== void 0) {
    add(depsMap.get(key))
  }

  const run = (effect: ReactiveEffect) => {
    if (effect.options.scheduler) {
      effect.options.scheduler(effect)
    } else {
      effect()
    }
  }

  // 先运行计算属性
  computedRunners.forEach(run)
  // 再运行普通效果
  effects.forEach(run)
}

// 创建响应式对象
export function reactive<T extends object>(target: T): T {
  if (isReactive(target)) {
    return target
  }
  return createReactiveObject(target)
}

// 判断是否是响应式对象
export function isReactive(value: unknown): boolean {
  return !!(value && (value as any)[ReactiveFlags.IS_REACTIVE])
}

// 获取原始对象
export function toRaw<T>(observed: T): T {
  const raw = observed && (observed as any)[ReactiveFlags.RAW]
  return raw ? toRaw(raw) : observed
}

// 工具函数：判断是否是对象
function isObject(val: unknown): val is Record<any, any> {
  return val !== null && typeof val === 'object'
}

// 创建响应式对象的处理器
const baseHandlers: ProxyHandler<any> = {
  get(target: object, key: string | symbol, receiver: object) {
    // 处理特殊 key
    if (key === ReactiveFlags.IS_REACTIVE) {
      return true
    } else if (key === ReactiveFlags.RAW) {
      return target
    }

    const res = Reflect.get(target, key, receiver)

    // 追踪依赖
    track(target, 'get', key)

    if (isObject(res)) {
      // 深层响应式
      return reactive(res)
    }

    return res
  },

  set(target: object, key: string | symbol, value: unknown, receiver: object) {
    const oldValue = (target as any)[key]
    const hadKey = Object.prototype.hasOwnProperty.call(target, key)
    const result = Reflect.set(target, key, value, receiver)

    if (target === toRaw(receiver)) {
      if (!hadKey) {
        trigger(target, 'add', key, value, oldValue)
      } else if (value !== oldValue) {
        trigger(target, 'set', key, value, oldValue)
      }
    }

    return result
  },

  deleteProperty(target: object, key: string | symbol) {
    const hadKey = Object.prototype.hasOwnProperty.call(target, key)
    const oldValue = (target as any)[key]
    const result = Reflect.deleteProperty(target, key)

    if (result && hadKey) {
      trigger(target, 'delete', key, undefined, oldValue)
    }

    return result
  },

  has(target: object, key: string | symbol) {
    const result = Reflect.has(target, key)
    track(target, 'has', key)
    return result
  },

  ownKeys(target: object) {
    track(target, 'iterate', undefined)
    return Reflect.ownKeys(target)
  }
}

// 创建响应式对象
function createReactiveObject<T extends object>(target: T): T {
  return new Proxy(target, baseHandlers)
}

// 任务优先级
const enum JobPriority {
  HIGH = 1,
  NORMAL = 2,
  LOW = 3
}

// 任务接口
interface Job {
  id?: number
  priority: JobPriority
  fn: () => void
}

// 批量更新控制
let isFlushing = false
let isFlushPending = false
const queue: Job[] = []
const resolvedPromise = Promise.resolve() as Promise<any>
let currentFlushPromise: Promise<void> | null = null

// 当前作用域
let activeEffectScope: EffectScope | null = null

// 作用域接口
export interface EffectScope {
  active: boolean
  effects: ReactiveEffect[]
  cleanups: (() => void)[]
  parent: EffectScope | null
  scopes: EffectScope[] | undefined
  run<T>(fn: () => T): T | undefined
  stop(): void
}

// 创建作用域
export function createEffectScope(detached = false): EffectScope {
  const scope: EffectScope = {
    active: true,
    effects: [],
    cleanups: [],
    parent: activeEffectScope,
    scopes: undefined,

    run(fn) {
      if (!scope.active) return

      const prevScope = activeEffectScope
      try {
        activeEffectScope = scope
        return fn()
      } finally {
        activeEffectScope = prevScope
      }
    },

    stop() {
      if (!scope.active) return

      scope.active = false
      scope.effects.forEach(effect => effect.stop?.())
      scope.cleanups.forEach(cleanup => cleanup())
      scope.scopes?.forEach(childScope => childScope.stop())
      scope.scopes = undefined
    }
  }

  if (!detached && activeEffectScope) {
    (activeEffectScope.scopes || (activeEffectScope.scopes = [])).push(scope)
  }

  return scope
}

// 获取当前作用域
export function getCurrentScope(): EffectScope | null {
  return activeEffectScope
}

// 在作用域停止时执行清理
export function onScopeDispose(fn: () => void) {
  if (activeEffectScope) {
    activeEffectScope.cleanups.push(fn)
  }
}

// 下一个微任务执行回调
export function nextTick(): Promise<void>
export function nextTick<T>(fn: () => T | Promise<T>): Promise<T>
export function nextTick<T>(fn?: () => T | Promise<T>): Promise<void | T> {
  const p = currentFlushPromise || resolvedPromise
  return fn ? p.then(fn) : p
}

// 将任务加入队列
export function queueJob(fn: () => void, priority: JobPriority = JobPriority.NORMAL) {
  const job: Job = {
    id: (fn as ReactiveEffect).id,
    priority,
    fn
  }

  if (!queue.some(j => j.fn === fn)) {
    queue.push(job)
    queueFlush()
  }
}

// 将预回调加入队列
export function queuePreFlushCb(cb: () => void) {
  queueJob(cb, JobPriority.HIGH)
}

// 将后回调加入队列
export function queuePostFlushCb(cb: () => void) {
  queueJob(cb, JobPriority.LOW)
}

// 触发队列刷新
function queueFlush() {
  if (!isFlushing && !isFlushPending) {
    isFlushPending = true
    currentFlushPromise = resolvedPromise.then(flushJobs)
  }
}

// 刷新任务队列
function flushJobs() {
  isFlushPending = false
  isFlushing = true

  try {
    // 按优先级和 ID 排序
    queue.sort((a, b) => {
      const diff = a.priority - b.priority
      return diff === 0 ? (a.id || 0) - (b.id || 0) : diff
    })

    for (let i = 0; i < queue.length; i++) {
      const job = queue[i]
      try {
        job.fn()
      } catch (error) {

      }
    }
  } finally {
    isFlushing = false
    queue.length = 0
    currentFlushPromise = null
  }
}

// 批量操作
export function batch<T>(fn: () => T): T {
  const scope = createEffectScope(true)
  try {
    return scope.run(fn)!
  } finally {
    scope.stop()
  }
}

// 立即执行回调
export function immediateEffect(fn: () => void) {
  queueJob(fn, JobPriority.HIGH)
}

// 延迟执行回调
export function deferredEffect(fn: () => void) {
  queueJob(fn, JobPriority.LOW)
}
