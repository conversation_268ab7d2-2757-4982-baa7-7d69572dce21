// 全局计数器和中断控制器
let totalCheckedCount = 0
let abortController: AbortController | null = null

// 重置计数
export function resetCount() {
  totalCheckedCount = 0
}

// 增加计数
export function incrementCount(count: number) {
  totalCheckedCount += count
}

// 获取总计数
export function getTotalCount() {
  return totalCheckedCount
}

// 创建新的中断控制器
export function createAbortController() {
  abortController = new AbortController()
  return abortController.signal
}

// 触发中断
export function abort() {
  if (abortController) {
    abortController.abort()
    abortController = null
  }
}
