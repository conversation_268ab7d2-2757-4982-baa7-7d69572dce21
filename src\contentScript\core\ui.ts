import { createUIObserver } from '../dom/observers'
import { panelStyles } from '../components/styles/styles'
import { loadPanelSettings } from '../components/state'
import { createFloatingMenu } from '../components/FloatingMenu'
import { logger } from '../utils/logger'

async function setupPanels() {
  // 检查是否已存在面板
  const existingPanel = document.querySelector('.tiktok-panel-container')
  if (existingPanel) {
    return // 如果已存在面板，则不重复创建
  }

  // 注入样式
  const styleElement = document.createElement('style')
  styleElement.textContent = panelStyles
  document.head.appendChild(styleElement)

  try {
    // 创建组件
    const mainContainer = await createFloatingMenu()
    if (!mainContainer) {
      throw new Error('创建浮动菜单失败')
    }

    // 添加到页面
    document.body.appendChild(mainContainer)

    // 初始化面板状态
    const logState = logger.getState()
    const logPanel = document.querySelector('.log-panel')
    if (logPanel) {
      logPanel.classList.toggle('visible', logState.value.isVisible)
    }

    return mainContainer
  } catch (error) {

    // 清理已创建的元素
    const panel = document.querySelector('.tiktok-panel-container')
    if (panel) {
      panel.remove()
    }
    const logPanel = document.querySelector('.log-panel')
    if (logPanel) {
      logPanel.remove()
    }
    throw error
  }
}

export const setupUI = async () => {
  try {
    // 清理已存在的面板和样式
    const existingPanels = document.querySelectorAll('.tiktok-panel-container')
    existingPanels.forEach(panel => {
      const container = panel as HTMLElement & { cleanup?: () => void }
      if (typeof container.cleanup === 'function') {
        container.cleanup()
      }
      panel.remove()
    })

    const existingStyles = document.querySelectorAll('style')
    existingStyles.forEach(style => {
      if (style.textContent?.includes('tiktok-panel-container')) {
        style.remove()
      }
    })

    await loadPanelSettings()
    const container = await setupPanels()

    // 如果创建失败，使用 UIObserver 重试
    if (!container) {
      return createUIObserver(loadPanelSettings, setupPanels)
    }
  } catch (error) {
    return createUIObserver(loadPanelSettings, setupPanels)
  }
}
