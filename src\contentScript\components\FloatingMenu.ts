import { getWechatNumber, delay } from '../utils/utils'
import { panelState, savePanelSettings, logState } from './state'
import { startReport } from '../services/reportService'
import { createLogPanel, setupLogPanel } from './LogPanel'
import { authGuard } from '../../utils/auth'
import { toast } from './toast'
import { messageBox } from './messageBox'
import { authPanel } from './AuthPanel'
import { effect } from '../utils/reactive'
import { abort, getTotalCount, resetCount } from '../core/taskManager'




// 添加防抖函数
function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// 扩展 HTMLDivElement 类型
interface FloatingMenuContainer extends HTMLDivElement {
  cleanup?: () => void;
}

// 添加任务完成事件监听
function setupTaskCompletionListener(autoSubmitButton: HTMLButtonElement) {
  window.addEventListener('taskCompleted', ((event: CustomEvent) => {
    // 恢复按钮状态
    panelState.value.isRunning = false
    autoSubmitButton.querySelector('.button-text')!.textContent = '运行'
    autoSubmitButton.querySelector('.button-icon')!.textContent = '▶️'
  }) as EventListener)
}

// 添加自动提报按钮事件监听
function setupAutoSubmitButton(autoSubmitButton: HTMLButtonElement) {
  autoSubmitButton.addEventListener('click', async () => {
    try {
      // 检查授权
      const isAuthorized = await authGuard()
      if (!isAuthorized) {
        await messageBox.error(
          '请先完成授权后再使用此功能',
          true,
          async () => {
            await authPanel.show()
          }
        )
        return
      }

      // 切换运行状态
      if (panelState.value.isRunning) {
        // 停止运行
        panelState.value.isRunning = false
        autoSubmitButton.querySelector('.button-text')!.textContent = '运行'
        autoSubmitButton.querySelector('.button-icon')!.textContent = '▶️'

        // 触发中断
        abort()

        // 显示总计数
        const totalCount = getTotalCount()
        toast.success(`提报已停止，共选中 ${totalCount} 个商品`)

        // 重置计数
        resetCount()
      } else {
        // 开始运行
        panelState.value.isRunning = true
        autoSubmitButton.querySelector('.button-text')!.textContent = '停止'
        autoSubmitButton.querySelector('.button-icon')!.textContent = '⏹️'

        // 获取当前按钮
        const currentButton = document.querySelector('.custom-inject-btn') as HTMLElement
        if (!currentButton) {
          toast.error('未找到可提报的商品')
          return
        }

        // 开始提报
        await startReport(currentButton)
      }
    } catch (error) {

      toast.error('自动提报出错，请查看日志')
      panelState.value.isRunning = false
      autoSubmitButton.querySelector('.button-text')!.textContent = '运行'
      autoSubmitButton.querySelector('.button-icon')!.textContent = '▶️'

      // 重置计数
      resetCount()
    }
  })
}

export async function createFloatingMenu() {
  const mainContainer = document.createElement('div') as FloatingMenuContainer
  mainContainer.className = 'tiktok-panel-container'

  // 创建最小化按钮
  const minimizeButton = document.createElement('button')
  minimizeButton.className = 'minimize-button'
  minimizeButton.innerHTML = '⌄'
  minimizeButton.title = '最小化面板'

  // 创建工具栏主体
  const toolbarContent = document.createElement('div')
  toolbarContent.className = 'toolbar-content'

  // 创建公共配置区域
  const commonConfigSection = document.createElement('div')
  commonConfigSection.className = 'toolbar-section config'
  commonConfigSection.innerHTML = `
    <div class="toolbar-section-title">公共配置</div>
    <div class="toolbar-controls">
      <div class="control-item">
        <label>操作间隔(秒)</label>
        <input type="number" id="operation-interval" min="0" value="0" step="any">
      </div>
      <div class="control-item">
        <label>超时时间(秒)</label>
        <input type="number" id="timeout" min="0" value="60" step="any">
      </div>
    </div>
  `

  // 创建自动提报区域
  const submitSection = document.createElement('div')
  submitSection.className = 'toolbar-section submit'
  submitSection.innerHTML = `
    <div class="toolbar-section-title">自动提报</div>
    <div class="toolbar-controls">
      <div class="control-item">
        <label>选择模式</label>
        <select id="panel-mode">
          <option value="fullPage">整页提报</option>
          <option value="select">选择提报</option>
        </select>
      </div>
      <div class="control-item">
        <label>选品策略</label>
        <select id="select-strategy">
          <option value="first">前5个</option>
          <option value="random">随机5个</option>
          <option value="custom">自定义起始</option>
        </select>
      </div>
      <div class="control-item" id="custom-start-container" style="display: none;">
        <label>起始位置</label>
        <input type="number" id="custom-start-index" min="1" value="1">
      </div>
      <div class="control-item">
        <label>从第几个开始</label>
        <input type="number" id="continue-from-index" min="1" value="1" title="从第几个商品开始处理，用于中断后继续">
      </div>
      <div class="control-item">
        <label>关键词优化</label>
        <label class="switch">
          <input type="checkbox" id="keyword-optimize">
          <span class="slider"></span>
        </label>
      </div>
      <button id="auto-submit" class="run-button">
        <span class="button-icon">▶️</span>
        <span class="button-text">运行</span>
      </button>
    </div>
  `

  // TODO：创建自动优化区域 功能待定
/*   const optimizeSection = document.createElement('div')
  optimizeSection.className = 'toolbar-section optimize'
  optimizeSection.innerHTML = `
    <div class="toolbar-section-title">自动优化</div>
    <div class="toolbar-controls">
      <div class="control-item">
        <label>选择模式</label>
        <select id="optimize-mode">
          <option value="fullPage">整页优化</option>
          <option value="autoPage">自动翻页</option>
        </select>
      </div>
      <button id="optimize-toggle" class="run-button">
        <span class="button-icon">▶️</span>
        <span class="button-text">运行</span>
      </button>
    </div>
  ` */

  // 创建信息区域
  const infoSection = document.createElement('div')
  infoSection.className = 'toolbar-section'
  infoSection.innerHTML = `
    <div class="wechat-info">更多玩法、联系作者：${await getWechatNumber()}</div>
    <div class="button-group">
      <button class="log-button">
        <span class="log-button-icon">📝</span>
        <span>操作日志</span>
      </button>
      <button class="auth-button">
        <span class="auth-button-icon">🔑</span>
        <span>授权信息</span>
      </button>
    </div>
  `

  // 添加按钮组样式
  const style = document.createElement('style')
  style.textContent = `
    .button-group {
      display: flex;
      gap: 8px;
      margin-top: 8px;
    }
    .auth-button {
      height: 28px;
      padding: 0 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      color: #666;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      white-space: nowrap;
      transition: all 0.3s ease;
    }
    .auth-button:hover {
      border-color: #3b82f6;
      color: #3b82f6;
      background: #eff6ff;
    }
    .auth-button.active {
      background: #3b82f6;
      border-color: #3b82f6;
      color: #fff;
    }
    .auth-button-icon {
      font-size: 14px;
    }
  `
  document.head.appendChild(style)

  // 组装工具栏
  toolbarContent.appendChild(commonConfigSection)
  toolbarContent.appendChild(submitSection)
/*   toolbarContent.appendChild(optimizeSection) */
  toolbarContent.appendChild(infoSection)

  mainContainer.appendChild(minimizeButton)
  mainContainer.appendChild(toolbarContent)

  // 创建并设置日志面板
  const logPanel = createLogPanel()
  document.body.appendChild(logPanel)

  // 设置日志面板的位置
  const updateLogPanelPosition = () => {
    const updatePosition = () => {
      const toolbarContent = mainContainer.querySelector('.toolbar-content') as HTMLElement
      if (!toolbarContent) return false

      const contentRect = toolbarContent.getBoundingClientRect()
      if (contentRect.width > 0) {
        // 只更新水平位置和宽度
        logPanel.style.left = `${contentRect.left}px`
        logPanel.style.width = `${contentRect.width}px`

        // 根据最小化状态调整垂直位置
        if (mainContainer.classList.contains('minimized')) {
          logPanel.style.top = '100%'
        } else if (logState.value.isVisible) {
          logPanel.style.top = `calc(100% - 356px)` // 300px height + 56px bottom margin
        } else {
          logPanel.style.top = '100%'
        }


        return true
      }
      return false
    }

    // 尝试立即更新
    if (!updatePosition()) {
      // 如果失败，等待DOM更新后重试
      requestAnimationFrame(() => {
        if (!updatePosition()) {
          // 如果仍然失败，设置一个延迟重试
          setTimeout(updatePosition, 100)
        }
      })
    }
  }

  // 创建防抖版本的位置更新函数
  const updateLogPanelPositionDebounced = debounce(updateLogPanelPosition, 16)

  // 使用 ResizeObserver 监听尺寸变化
  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (entry.target === mainContainer) {
        updateLogPanelPositionDebounced()
      }
    }
  })

  // 开始监听尺寸变化
  resizeObserver.observe(mainContainer)

  // 监听各种可能影响位置的事件
  window.addEventListener('resize', updateLogPanelPositionDebounced)
  window.addEventListener('scroll', updateLogPanelPositionDebounced)
  document.addEventListener('visibilitychange', updateLogPanelPositionDebounced)

  // 创建 MutationObserver 监听主容器的变化
  const observer = new MutationObserver((mutations) => {
    let needsUpdate = false
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' || mutation.type === 'childList') {
        needsUpdate = true
      }
    })
    if (needsUpdate) {
      updateLogPanelPositionDebounced()
    }
  })

  observer.observe(mainContainer, {
    attributes: true,
    childList: true,
    subtree: true
  })

  // 初始化位置
  updateLogPanelPosition()

  // 获取日志按钮引用
  const logButton = mainContainer.querySelector('.log-button') as HTMLElement
  if (!logButton) {

    return
  }

  // 获取授权按钮引用
  const authButton = mainContainer.querySelector('.auth-button') as HTMLElement
  if (!authButton) {
    return
  }

  // 初始化日志面板
  const cleanup = setupLogPanel(logPanel)

  // 添加最小化功能
  minimizeButton.addEventListener('click', () => {
    mainContainer.classList.toggle('minimized')
    minimizeButton.innerHTML = mainContainer.classList.contains('minimized') ? '⌃' : '⌄'
    minimizeButton.title = mainContainer.classList.contains('minimized') ? '展开面板' : '最小化面板'
    panelState.value.isPanelMinimized = mainContainer.classList.contains('minimized')

    // 立即更新位置
    updateLogPanelPosition()
    // 延迟再次更新以确保过渡动画完成后的位置正确
    setTimeout(updateLogPanelPosition, 300)
  })

  // 添加运行状态切换
  const runButtons = mainContainer.querySelectorAll('.run-button')
  runButtons.forEach((button) => {
    button.addEventListener('click', () => {
      const isRunning = button.classList.contains('running')
      const buttonText = button.querySelector('.button-text')!
      const buttonIcon = button.querySelector('.button-icon')!

      if (!isRunning) {
        button.classList.add('running')
        buttonIcon.textContent = '⏹️'
        buttonText.textContent = '停止'

        // 如果是优化按钮，移除原有事件
        if (button.id === 'optimize-toggle') {
          document.dispatchEvent(new CustomEvent('removeOptimizeEvents'))
        }
      } else {
        button.classList.remove('running')
        buttonIcon.textContent = '▶️'
        buttonText.textContent = '运行'
      }
    })
  })

  // 添加日志面板切换
  logButton.addEventListener('click', (e) => {
    e.preventDefault()
    e.stopPropagation()

    const isVisible = !logState.value.isVisible


    // 更新按钮状态
    logButton.classList.toggle('active', isVisible)

    // 更新日志状态
    logState.value.isVisible = isVisible

    // 更新面板位置
    updateLogPanelPosition()

    // 保存设置
    savePanelSettings()
  })

  // 同步操作间隔和翻页间隔
  const operationInterval = mainContainer.querySelector('#operation-interval') as HTMLInputElement
  const timeout = mainContainer.querySelector('#timeout') as HTMLInputElement
  const keywordOptimize = mainContainer.querySelector('#keyword-optimize') as HTMLInputElement
  const panelMode = mainContainer.querySelector('#panel-mode') as HTMLSelectElement
  const autoSubmitButton = mainContainer.querySelector('#auto-submit') as HTMLButtonElement
  const selectStrategy = mainContainer.querySelector('#select-strategy') as HTMLSelectElement
  const customStartIndex = mainContainer.querySelector('#custom-start-index') as HTMLInputElement
  const customStartContainer = mainContainer.querySelector('#custom-start-container') as HTMLElement
  const continueFromIndex = mainContainer.querySelector('#continue-from-index') as HTMLInputElement

  // 添加响应式效果，监听关键词增强状态变化
  effect(() => {
    const useKeywords = panelState.value.reportUseKeywords
    if (keywordOptimize.checked !== useKeywords) {
      keywordOptimize.checked = useKeywords
    }
  })

  operationInterval.addEventListener('change', async () => {
    panelState.value.operationInterval = parseFloat(operationInterval.value)
    await savePanelSettings()
  })

  timeout.addEventListener('change', async () => {
    panelState.value.timeout = parseFloat(timeout.value)
    await savePanelSettings()
  })

  keywordOptimize.addEventListener('change', async () => {
    // 避免循环更新
    if (panelState.value.reportUseKeywords !== keywordOptimize.checked) {
      panelState.value.reportUseKeywords = keywordOptimize.checked
      await savePanelSettings()
    }
  })

  // 添加模式选择事件监听
  panelMode.addEventListener('change', async () => {
    panelState.value.reportMode = panelMode.value as 'select' | 'fullPage'
    await savePanelSettings()
  })

  // 添加选品策略事件监听
  selectStrategy.addEventListener('change', async () => {
    panelState.value.selectStrategy = selectStrategy.value as 'first' | 'random' | 'custom'
    // 根据选择显示或隐藏自定义起始位置输入框
    customStartContainer.style.display = selectStrategy.value === 'custom' ? 'block' : 'none'
    await savePanelSettings()
  })

  // 添加自定义起始位置事件监听
  customStartIndex.addEventListener('change', async () => {
    const value = parseInt(customStartIndex.value)
    // 确保值不小于1
    if (value < 1) {
      customStartIndex.value = '1'
      panelState.value.customStartIndex = 1
    } else {
      panelState.value.customStartIndex = value
    }
    await savePanelSettings()
  })

  // 添加"从第几个继续"事件监听
  continueFromIndex.addEventListener('change', async () => {
    const value = parseInt(continueFromIndex.value)
    // 确保值不小于1
    if (value < 1) {
      continueFromIndex.value = '1'
      panelState.value.continueFromIndex = 1
    } else {
      panelState.value.continueFromIndex = value
    }
    await savePanelSettings()
  })

  // 添加运行按钮点击事件
  setupAutoSubmitButton(autoSubmitButton)

  // 监听任务状态变化
  let hasClearedQueue = false
  effect(() => {
    if (!panelState.value.isRunning && !hasClearedQueue) {
      hasClearedQueue = true
      autoSubmitButton.querySelector('.button-text')!.textContent = '运行'
      autoSubmitButton.querySelector('.button-icon')!.textContent = '▶️'
    } else if (panelState.value.isRunning) {
      hasClearedQueue = false
    }
  })

  // 使用响应式effect监听运行状态
  effect(() => {
    const buttonText = autoSubmitButton.querySelector('.button-text')!
    const buttonIcon = autoSubmitButton.querySelector('.button-icon')!

    if (panelState.value.isRunning) {
      buttonText.textContent = '停止'
      buttonIcon.textContent = '⏹'
    } else {
      buttonText.textContent = '运行'
      buttonIcon.textContent = '▶️'
    }
  })

  // 设置初始值
  operationInterval.value = panelState.value.operationInterval.toString()
  timeout.value = panelState.value.timeout.toString()
  keywordOptimize.checked = panelState.value.reportUseKeywords
  panelMode.value = panelState.value.reportMode
  selectStrategy.value = panelState.value.selectStrategy
  customStartIndex.value = panelState.value.customStartIndex.toString()
  continueFromIndex.value = panelState.value.continueFromIndex.toString()

  // 根据初始选品策略设置自定义起始位置输入框的显示状态
  customStartContainer.style.display = panelState.value.selectStrategy === 'custom' ? 'block' : 'none'

  // 根据状态设置初始显示状态
  if (panelState.value.isPanelMinimized) {
    mainContainer.classList.add('minimized')
    minimizeButton.innerHTML = '⌃'
    minimizeButton.title = '展开面板'
  }

  // 添加授权按钮点击事件
  authButton.addEventListener('click', async () => {
    try {
      await authPanel.show()
    } catch (error) {
      toast.error('获取授权信息时发生错误')
    }
  })

  // 添加清理函数
  mainContainer.cleanup = () => {
    // 清理观察器
    observer.disconnect()
    resizeObserver.disconnect()
    // 移除事件监听器
    window.removeEventListener('resize', updateLogPanelPositionDebounced)
    window.removeEventListener('scroll', updateLogPanelPositionDebounced)
    document.removeEventListener('visibilitychange', updateLogPanelPositionDebounced)
    // 移除日志面板
    if (logPanel && logPanel.parentNode) {
      logPanel.parentNode.removeChild(logPanel)
    }
    // 清理日志面板事件
    if (typeof cleanup === 'function') {
      cleanup()
    }
  }

  return mainContainer
}
