import { defineConfig, presetIcons, presetTypography, presetWind4 } from 'unocss'


export default defineConfig({
  presets: [
    presetWind4({
        reset: true,
    }),
    presetIcons({
      scale: 1.2,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
      collections: {
        ri: () => import("@iconify-json/ri/icons.json").then((i) => i.default),
      },
    }),
    presetTypography({
      cssExtend: {
        'h1,h2,h3': {
          'color': '#42b883',
          'font-weight': '600',
          'margin-top': '2rem',
          'margin-bottom': '1.2rem'
        },
        'ul': {
          'list-style-type': 'none',
          'padding-left': '1rem'
        },
        'ul li': {
          'position': 'relative',
          'margin-top': '0.5rem',
          'margin-bottom': '0.5rem'
        },
        'ul li:before': {
          'content': '""',
          'width': '6px',
          'height': '6px',
          'position': 'absolute',
          'left': '-1rem',
          'top': '0.6rem',
          'background-color': '#42b883',
          'border-radius': '50%'
        }
      }
    }),
  ],
  theme: {
    colors: {
      primary: {
        DEFAULT: "#42b883", // Vue 绿色
        50: "#ecfdf5",
        100: "#d1fae5",
        200: "#a7f3d0",
        300: "#6ee7b7",
        400: "#34d399",
        500: "#42b883", // 主色
        600: "#059669",
        700: "#047857",
        800: "#065f46",
        900: "#064e3b",
        dark: "#34a873", // 暗色模式主色
      },
      secondary: {
        DEFAULT: "#7957d5", // 紫色作为次色
        50: "#f5f3ff",
        100: "#ede9fe",
        200: "#ddd6fe",
        300: "#c4b5fd",
        400: "#a78bfa",
        500: "#7957d5", // 次色
        600: "#7c3aed",
        700: "#6d28d9",
        800: "#5b21b6",
        900: "#4c1d95",
        dark: "#6a4bc0", // 暗色模式次色
      },
      success: {
        DEFAULT: "#10b981", // 绿色
        50: "#ecfdf5",
        100: "#d1fae5",
        200: "#a7f3d0",
        300: "#6ee7b7",
        400: "#34d399",
        500: "#10b981", // 成功色
        600: "#059669",
        700: "#047857",
        800: "#065f46",
        900: "#064e3b",
        dark: "#0ea271", // 暗色模式成功色
      },
      info: {
        DEFAULT: "#3b82f6", // 蓝色
        50: "#eff6ff",
        100: "#dbeafe",
        200: "#bfdbfe",
        300: "#93c5fd",
        400: "#60a5fa",
        500: "#3b82f6", // 信息色
        600: "#2563eb",
        700: "#1d4ed8",
        800: "#1e40af",
        900: "#1e3a8a",
        dark: "#2563eb", // 暗色模式信息色
      },
      warning: {
        DEFAULT: "#f59e0b", // 黄色
        50: "#fffbeb",
        100: "#fef3c7",
        200: "#fde68a",
        300: "#fcd34d",
        400: "#fbbf24",
        500: "#f59e0b", // 警告色
        600: "#d97706",
        700: "#b45309",
        800: "#92400e",
        900: "#78350f",
        dark: "#d97706", // 暗色模式警告色
      },
      error: {
        DEFAULT: "#ef4444", // 红色
        50: "#fef2f2",
        100: "#fee2e2",
        200: "#fecaca",
        300: "#fca5a5",
        400: "#f87171",
        500: "#ef4444", // 错误色
        600: "#dc2626",
        700: "#b91c1c",
        800: "#991b1b",
        900: "#7f1d1d",
        dark: "#dc2626", // 暗色模式错误色
      },
    }
  },
  rules: [
    [/^text-(.*)(?:-(.*))?$/, ([, c, shade], { theme }: { theme: any }) => {
      if (theme.colors[c])
        return { "color": theme.colors[c][shade || 'DEFAULT'] }
    }],
    [/^bg-(.*)(?:-(.*))?$/, ([, c, shade], { theme }) => {
      if (theme.colors[c])
        return { "background-color": theme.colors[c][shade || 'DEFAULT'] }
    }],
    [/^b-(.*)(?:-(.*))?$/, ([, c, shade], { theme }) => {
      if (theme.colors[c])
        return { "border-color": theme.colors[c][shade || 'DEFAULT'] }
    }],
  ],
  shortcuts: {
    "theme-bg": "bg-white dark:bg-gray-900",
    "theme-bg-secondary": "bg-gray-100 dark:bg-gray-800",
    "theme-text": "text-gray-900 dark:text-gray-100 font-sans text-base",
    "theme-text-secondary": "text-gray-600 dark:text-gray-400 font-sans text-base",
  }
})
