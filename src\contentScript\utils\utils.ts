import { panelState } from '../components/state'

/**
 * 延迟指定的秒数，支持中断信号
 */
export async function delay(seconds: number, signal?: AbortSignal): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    const timeoutId = setTimeout(resolve, seconds * 1000)

    if (signal) {
      // 如果信号已经是 aborted 状态，直接拒绝
      if (signal.aborted) {
        clearTimeout(timeoutId)
        reject(new Error('Operation aborted'))
        return
      }

      // 监听中断信号
      signal.addEventListener('abort', () => {
        clearTimeout(timeoutId)
        reject(new Error('Operation aborted'))
      }, { once: true })
    }
  })
}

/**
 * 智能延迟函数，当最大值大于等于0时，在最大值的一半到最大值之间随机延迟
 * @param max 最大延迟时间（秒），范围：>=0秒
 */
export async function smartDelay(max: number, signal?: AbortSignal): Promise<void> {
  // 确保延迟时间不小于0
  const validMax = Math.max(0, max)

  // 只要大于等于0就计算最小延时
  const minDelay = validMax >= 0 ? validMax / 2 : 0
  const delayTime = minDelay + (Math.random() * (validMax - minDelay))
  await delay(delayTime, signal)
}

/**
 * 等待元素出现并返回该元素，支持中断信号
 */
export async function waitForElementAppear(selector: string, timeout: number = 30, signal?: AbortSignal): Promise<HTMLElement | null> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    let intervalId: number

    // 如果信号已经是 aborted 状态，直接拒绝
    if (signal?.aborted) {
      reject(new Error('Operation aborted'))
      return
    }

    const cleanup = () => {
      clearInterval(intervalId)
    }

    const checkElement = () => {
      const element = document.querySelector(selector)

      // 检查元素是否存在且已经完全加载到DOM中
      if (element instanceof HTMLElement && element.isConnected) {
        // 额外检查元素是否真的可见和可交互
        const style = window.getComputedStyle(element)
        if (style.display !== 'none' && style.visibility !== 'hidden') {
          cleanup()
          resolve(element)
          return
        }
      }

      // 检查是否超时
      if (Date.now() - startTime > timeout * 1000) {
        cleanup()

        reject(new Error('页面响应超时。请在设置面板中增加超时时间和翻页间隔的时间，这样可以避免大部分元素无法获取的问题'))
      }
    }

    intervalId = window.setInterval(checkElement, 10)
    checkElement() // 立即检查一次

    // 监听中断信号
    if (signal) {
      signal.addEventListener('abort', () => {
        cleanup()
        reject(new Error('Operation aborted'))
      }, { once: true })
    }
  })
}

export async function waitForElementDisappear(selector: string, timeout: number = 30, signal?: AbortSignal): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    let intervalId: number

    // 如果信号已经是 aborted 状态，直接拒绝
    if (signal?.aborted) {
      reject(new Error('Operation aborted'))
      return
    }

    const cleanup = () => {
      clearInterval(intervalId)
    }

    const checkElement = () => {
      const element = document.querySelector(selector)

      // 如果元素不存在，则认为已经消失
      if (!element) {
        cleanup()
        resolve(true)
        return
      }

      // 如果元素存在，检查是否可见
      if (element instanceof HTMLElement) {
        const style = window.getComputedStyle(element)
        if (style.display === 'none' || style.visibility === 'hidden') {
          cleanup()
          resolve(true)
          return
        }
      }

      // 检查是否超时
      if (Date.now() - startTime > timeout * 1000) {
        cleanup()

        resolve(false)
      }
    }

    intervalId = window.setInterval(checkElement, 10)
    checkElement() // 立即检查一次

    // 监听中断信号
    if (signal) {
      signal.addEventListener('abort', () => {
        cleanup()
        reject(new Error('Operation aborted'))
      }, { once: true })
    }
  })
}

/**
 * 查找元素并点击，支持自定义延迟时间和中断信号
 */
export async function clickElementWithDelay(selector: string, timeout: number = 30, delayTime: number = 0, signal?: AbortSignal): Promise<void> {
  try {
    const element = await waitForElementAppear(selector, timeout, signal)
    if (element) {
      element.click()
      if (delayTime > 0) {
        await smartDelay(delayTime, signal)
      }
    }
  } catch (error) {
    if ((error as Error).message === 'Operation aborted') {
      throw error // 重新抛出中断错误
    }
    throw new Error('操作响应超时。建议：请适当增加操作间隔时间（建议3秒以上），这样可以大幅提高成功率')
  }
}

/**
 * 查找元素并点击，使用操作间隔延迟，支持中断信号
 */
export async function clickElementWithOperationDelay(selector: string, timeout: number = 10, signal?: AbortSignal): Promise<void> {
  try {
    const element = await waitForElementAppear(selector, timeout, signal)
    if (element) {
      element.click()
      await smartDelay(panelState.value.operationInterval, signal)
    }
  } catch (error) {
    if ((error as Error).message === 'Operation aborted') {
      throw error // 重新抛出中断错误
    }
    throw new Error('操作响应不稳定。请在设置面板中将"操作间隔时间"调整到3秒或更高，这样可以提高操作的稳定性')
  }
}



// 获取微信号
export async function getWechatNumber(): Promise<string> {
  const manifest = await chrome.runtime.getManifest()
  return (manifest as any)?.author?.email || 'D4K8888'
}
